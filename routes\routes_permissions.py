# routes_permissions.py
"""
Permission management API routes.
Provides endpoints for permission CRUD operations with authentication and authorization.
"""

from typing import List, Dict, Any
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query
from opentelemetry import trace
import logging

from utils.auth_bearer import JWTBearer
from controllers.permissions_controller import (
    create_permission_controller,
    get_permission_by_id_controller,
    get_permission_by_name_controller,
    update_permission_controller,
    delete_permission_controller,
    list_permissions_controller,
    get_permissions_by_category_controller,
    assign_permissions_to_role_controller,
    remove_permissions_from_role_controller,
    check_user_permission_controller,
    get_user_permissions_controller,
    get_role_permissions_controller,
    activate_permission_controller,
    get_permissions_for_role_assignment_controller
)
from models.permission import (
    Permission,
    PermissionCreate,
    PermissionUpdate,
    PermissionResponse,
    PermissionFilters,
    PermissionListResponse,
    PermissionsByCategory,
    RolePermissionAssignment,
    UserPermissionCheck,
    PermissionCheckResult
)

# Logging configuration
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
tracer = trace.get_tracer(__name__)

router = APIRouter()


@router.post("/", response_model=PermissionResponse, dependencies=[Depends(JWTBearer())])
def create_permission_endpoint(permission: PermissionCreate):
    """
    Create a new permission.

    Parameters:
    - **permission**: Permission creation data including name, description, category, resource, action

    Returns:
    - **PermissionResponse**: Created permission data with ID and timestamps

    Raises:
    - **400**: If permission name already exists or validation fails
    - **500**: If permission creation fails
    """
    try:
        logger.info(f"Creating permission with name: {permission.name}")
        if not permission.name or not permission.category or not permission.resource or not permission.action:
            raise HTTPException(status_code=400, detail="Name, category, resource, and action are required")
        
        result = create_permission_controller(permission)
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in create_permission_endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/", response_model=PermissionListResponse, dependencies=[Depends(JWTBearer())])
def list_permissions_endpoint(
    name: str = Query(None, description="Filter by permission name (partial match)"),
    category: str = Query(None, description="Filter by category"),
    resource: str = Query(None, description="Filter by resource (partial match)"),
    action: str = Query(None, description="Filter by action (partial match)"),
    is_active: bool = Query(None, description="Filter by active status"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Page size")
):
    """
    List permissions with filtering and pagination.

    Parameters:
    - **name**: Filter by permission name (partial match)
    - **category**: Filter by category (exact match)
    - **resource**: Filter by resource (partial match)
    - **action**: Filter by action (partial match)
    - **is_active**: Filter by active status
    - **page**: Page number (default: 1)
    - **page_size**: Page size (default: 10, max: 100)

    Returns:
    - **PermissionListResponse**: Paginated list of permissions with metadata
    """
    try:
        filters = PermissionFilters(
            name=name,
            category=category,
            resource=resource,
            action=action,
            is_active=is_active,
            page=page,
            page_size=page_size
        )
        return list_permissions_controller(filters)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in list_permissions_endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/by-category", response_model=List[PermissionsByCategory], dependencies=[Depends(JWTBearer())])
def get_permissions_by_category_endpoint():
    """
    Get permissions grouped by category.

    Returns:
    - **List[PermissionsByCategory]**: Permissions grouped by category
    """
    try:
        return get_permissions_by_category_controller()
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in get_permissions_by_category_endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{permission_id}", response_model=PermissionResponse, dependencies=[Depends(JWTBearer())])
def get_permission_by_id_endpoint(permission_id: UUID):
    """
    Get a permission by ID.

    Parameters:
    - **permission_id**: Permission UUID

    Returns:
    - **PermissionResponse**: Permission data

    Raises:
    - **404**: If permission not found
    """
    try:
        return get_permission_by_id_controller(permission_id)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in get_permission_by_id_endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/name/{name}", response_model=PermissionResponse, dependencies=[Depends(JWTBearer())])
def get_permission_by_name_endpoint(name: str):
    """
    Get a permission by name.

    Parameters:
    - **name**: Permission name

    Returns:
    - **PermissionResponse**: Permission data

    Raises:
    - **404**: If permission not found
    """
    try:
        return get_permission_by_name_controller(name)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in get_permission_by_name_endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.put("/{permission_id}", response_model=PermissionResponse, dependencies=[Depends(JWTBearer())])
def update_permission_endpoint(permission_id: UUID, permission: PermissionUpdate):
    """
    Update a permission.

    Parameters:
    - **permission_id**: Permission UUID
    - **permission**: Permission update data

    Returns:
    - **PermissionResponse**: Updated permission data

    Raises:
    - **404**: If permission not found
    - **400**: If update validation fails
    """
    try:
        return update_permission_controller(permission_id, permission)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in update_permission_endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/{permission_id}", dependencies=[Depends(JWTBearer())])
def delete_permission_endpoint(permission_id: UUID):
    """
    Delete a permission (soft delete).

    Parameters:
    - **permission_id**: Permission UUID

    Returns:
    - **Dict**: Success message

    Raises:
    - **404**: If permission not found
    - **400**: If permission cannot be deleted (assigned to roles)
    """
    try:
        return delete_permission_controller(permission_id)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in delete_permission_endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/{permission_id}/activate", response_model=PermissionResponse, dependencies=[Depends(JWTBearer())])
def activate_permission_endpoint(permission_id: UUID):
    """
    Activate a permission.

    Parameters:
    - **permission_id**: Permission UUID

    Returns:
    - **PermissionResponse**: Activated permission data

    Raises:
    - **404**: If permission not found
    """
    try:
        return activate_permission_controller(permission_id)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in activate_permission_endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/assign-to-role", dependencies=[Depends(JWTBearer())])
def assign_permissions_to_role_endpoint(assignment: RolePermissionAssignment):
    """
    Assign multiple permissions to a role.

    Parameters:
    - **assignment**: Role permission assignment data

    Returns:
    - **Dict**: Success message

    Raises:
    - **404**: If role or permissions not found
    """
    try:
        return assign_permissions_to_role_controller(assignment)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in assign_permissions_to_role_endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/remove-from-role/{role_id}", dependencies=[Depends(JWTBearer())])
def remove_permissions_from_role_endpoint(
    role_id: UUID, 
    permission_ids: List[UUID] = Query(..., description="List of permission IDs to remove")
):
    """
    Remove permissions from a role.

    Parameters:
    - **role_id**: Role UUID
    - **permission_ids**: List of permission UUIDs to remove

    Returns:
    - **Dict**: Success message

    Raises:
    - **404**: If role not found
    """
    try:
        return remove_permissions_from_role_controller(role_id, permission_ids)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in remove_permissions_from_role_endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/check", response_model=PermissionCheckResult, dependencies=[Depends(JWTBearer())])
def check_user_permission_endpoint(check_data: UserPermissionCheck):
    """
    Check if a user has a specific permission.

    Parameters:
    - **check_data**: User permission check data

    Returns:
    - **PermissionCheckResult**: Permission check result

    Raises:
    - **404**: If user not found
    """
    try:
        return check_user_permission_controller(check_data.user_id, check_data.permission_name)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in check_user_permission_endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/user/{user_id}", response_model=List[PermissionResponse], dependencies=[Depends(JWTBearer())])
def get_user_permissions_endpoint(user_id: UUID):
    """
    Get all permissions for a user.

    Parameters:
    - **user_id**: User UUID

    Returns:
    - **List[PermissionResponse]**: List of user's permissions

    Raises:
    - **404**: If user not found
    """
    try:
        return get_user_permissions_controller(user_id)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in get_user_permissions_endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/role/{role_id}", response_model=List[PermissionResponse], dependencies=[Depends(JWTBearer())])
def get_role_permissions_endpoint(role_id: UUID):
    """
    Get all permissions assigned to a role.

    Parameters:
    - **role_id**: Role UUID

    Returns:
    - **List[PermissionResponse]**: List of role's permissions

    Raises:
    - **404**: If role not found
    """
    try:
        return get_role_permissions_controller(role_id)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in get_role_permissions_endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/role/{role_id}/assignment-status", dependencies=[Depends(JWTBearer())])
def get_permissions_for_role_assignment_endpoint(role_id: UUID):
    """
    Get permissions with assignment status for a role.

    Parameters:
    - **role_id**: Role UUID

    Returns:
    - **List[Dict]**: Permissions with assignment status

    Raises:
    - **404**: If role not found
    """
    try:
        return get_permissions_for_role_assignment_controller(role_id)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in get_permissions_for_role_assignment_endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
