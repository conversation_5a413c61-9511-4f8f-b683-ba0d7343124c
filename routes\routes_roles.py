# routes_roles.py
"""
Role management API routes.
Provides endpoints for role CRUD operations with authentication and authorization.
"""

from typing import List, Dict, Any
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query
from opentelemetry import trace
import logging

from utils.auth_bearer import JWTBearer
from controllers.roles_controller import (
    create_role_controller,
    get_role_by_id_controller,
    get_role_by_name_controller,
    update_role_controller,
    delete_role_controller,
    list_roles_controller,
    get_role_hierarchy_controller,
    assign_role_to_user_controller,
    get_users_by_role_controller,
    get_role_permissions_controller,
    activate_role_controller
)
from models.role import (
    Role,
    RoleCreate,
    RoleUpdate,
    RoleResponse,
    RoleFilters,
    RoleListResponse,
    RoleHierarchy
)

# Logging configuration
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
tracer = trace.get_tracer(__name__)

router = APIRouter()


@router.post("/", response_model=RoleResponse, dependencies=[Depends(JWTBearer())])
def create_role_endpoint(role: RoleCreate):
    """
    Create a new role.

    Parameters:
    - **role**: Role creation data including name, description, hierarchy level, etc.

    Returns:
    - **RoleResponse**: Created role data with ID and timestamps

    Raises:
    - **400**: If role name already exists or validation fails
    - **500**: If role creation fails
    """
    try:
        logger.info(f"Creating role with name: {role.name}")
        if not role.name:
            raise HTTPException(status_code=400, detail="Role name is required")
        
        result = create_role_controller(role)
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in create_role_endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/", response_model=RoleListResponse, dependencies=[Depends(JWTBearer())])
def list_roles_endpoint(
    name: str = Query(None, description="Filter by role name (partial match)"),
    is_system_role: bool = Query(None, description="Filter by system role status"),
    hierarchy_level: int = Query(None, ge=0, le=10, description="Filter by hierarchy level"),
    parent_role_id: UUID = Query(None, description="Filter by parent role ID"),
    is_active: bool = Query(None, description="Filter by active status"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Page size")
):
    """
    List roles with filtering and pagination.

    Parameters:
    - **name**: Filter by role name (partial match)
    - **is_system_role**: Filter by system role status
    - **hierarchy_level**: Filter by hierarchy level (0-10)
    - **parent_role_id**: Filter by parent role ID
    - **is_active**: Filter by active status
    - **page**: Page number (default: 1)
    - **page_size**: Page size (default: 10, max: 100)

    Returns:
    - **RoleListResponse**: Paginated list of roles with metadata
    """
    try:
        filters = RoleFilters(
            name=name,
            is_system_role=is_system_role,
            hierarchy_level=hierarchy_level,
            parent_role_id=parent_role_id,
            is_active=is_active,
            page=page,
            page_size=page_size
        )
        return list_roles_controller(filters)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in list_roles_endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/hierarchy", response_model=List[RoleHierarchy], dependencies=[Depends(JWTBearer())])
def get_role_hierarchy_endpoint():
    """
    Get complete role hierarchy tree.

    Returns:
    - **List[RoleHierarchy]**: Hierarchical role structure with parent-child relationships
    """
    try:
        return get_role_hierarchy_controller()
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in get_role_hierarchy_endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{role_id}", response_model=RoleResponse, dependencies=[Depends(JWTBearer())])
def get_role_by_id_endpoint(role_id: UUID):
    """
    Get a role by ID.

    Parameters:
    - **role_id**: Role UUID

    Returns:
    - **RoleResponse**: Role data

    Raises:
    - **404**: If role not found
    """
    try:
        return get_role_by_id_controller(role_id)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in get_role_by_id_endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/name/{name}", response_model=RoleResponse, dependencies=[Depends(JWTBearer())])
def get_role_by_name_endpoint(name: str):
    """
    Get a role by name.

    Parameters:
    - **name**: Role name

    Returns:
    - **RoleResponse**: Role data

    Raises:
    - **404**: If role not found
    """
    try:
        return get_role_by_name_controller(name)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in get_role_by_name_endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.put("/{role_id}", response_model=RoleResponse, dependencies=[Depends(JWTBearer())])
def update_role_endpoint(role_id: UUID, role: RoleUpdate):
    """
    Update a role.

    Parameters:
    - **role_id**: Role UUID
    - **role**: Role update data

    Returns:
    - **RoleResponse**: Updated role data

    Raises:
    - **404**: If role not found
    - **400**: If update validation fails
    """
    try:
        return update_role_controller(role_id, role)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in update_role_endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/{role_id}", dependencies=[Depends(JWTBearer())])
def delete_role_endpoint(role_id: UUID):
    """
    Delete a role (soft delete).

    Parameters:
    - **role_id**: Role UUID

    Returns:
    - **Dict**: Success message

    Raises:
    - **404**: If role not found
    - **400**: If role cannot be deleted (system role or has users)
    """
    try:
        return delete_role_controller(role_id)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in delete_role_endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/{role_id}/activate", response_model=RoleResponse, dependencies=[Depends(JWTBearer())])
def activate_role_endpoint(role_id: UUID):
    """
    Activate a role.

    Parameters:
    - **role_id**: Role UUID

    Returns:
    - **RoleResponse**: Activated role data

    Raises:
    - **404**: If role not found
    """
    try:
        return activate_role_controller(role_id)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in activate_role_endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/{role_id}/assign-user/{user_id}", dependencies=[Depends(JWTBearer())])
def assign_role_to_user_endpoint(role_id: UUID, user_id: UUID, assigned_by: str = Query(..., description="Who assigned the role")):
    """
    Assign a role to a user.

    Parameters:
    - **role_id**: Role UUID
    - **user_id**: User UUID
    - **assigned_by**: Who assigned the role

    Returns:
    - **Dict**: Success message

    Raises:
    - **404**: If role or user not found
    """
    try:
        return assign_role_to_user_controller(user_id, role_id, assigned_by)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in assign_role_to_user_endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{role_id}/users", dependencies=[Depends(JWTBearer())])
def get_users_by_role_endpoint(role_id: UUID):
    """
    Get all users assigned to a role.

    Parameters:
    - **role_id**: Role UUID

    Returns:
    - **List[Dict]**: List of users with the role
    """
    try:
        return get_users_by_role_controller(role_id)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in get_users_by_role_endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{role_id}/permissions", dependencies=[Depends(JWTBearer())])
def get_role_permissions_endpoint(role_id: UUID):
    """
    Get all permissions assigned to a role.

    Parameters:
    - **role_id**: Role UUID

    Returns:
    - **List[Dict]**: List of permissions for the role
    """
    try:
        return get_role_permissions_controller(role_id)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in get_role_permissions_endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
