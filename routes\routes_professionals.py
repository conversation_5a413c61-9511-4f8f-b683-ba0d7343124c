# routes_professionals.py
from fastapi import APIRouter, HTTPException
from models.professionals import Professional, ProfessionalFilters, Professionals, ProfessionalCreate, ProfessionalUpdate
from controllers.professionals_controller import create_professional, update_professional, get_professional_by_id, fetch_all_professionals, get_professionals_page, delete_professional
from opentelemetry import trace  # NEW
import logging
import psycopg2

# Logging configuration
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
tracer = trace.get_tracer(__name__)


router = APIRouter()


# Create a new professional
@router.post("/", response_model=Professional)
def create_professional_endpoint(professional: ProfessionalCreate):
    '''
    Create a new professional.

    Parameters
    ----------
    professional : ProfessionalCreate
        The professional data to create.

    Returns
    -------
    Professional
        The created professional object.

    Raises
    ------
    HTTPException
        If professional creation fails.
    '''
    try:
        return create_professional(professional)
    except psycopg2.Error as e:
        logger.error(f"Database error create_professional_endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database Error create_professional_endpoint: {str(e)}")
    except HTTPException as http_exc:
        logger.error(f"HTTP Exception while creating professional: {str(http_exc.detail)}")
        raise http_exc
    except Exception as e:
        logger.error(f"Error creating professional: {str(e)}")
        raise HTTPException(status_code=500, detail="Error occurred while creating professional: {}".format(str(e)))


# Update a professional
@router.put("/", response_model=Professional)
def update_professional_endpoint(professional: ProfessionalUpdate):
    '''
    Update an existing professional.

    Parameters
    ----------
    professional : ProfessionalUpdate
        The professional data to update.

    Returns
    -------
    Professional
        The updated professional object.

    Raises
    ------
    HTTPException
        If professional update fails.
    '''
    try:
        return update_professional(professional)
    except psycopg2.Error as e:
        logger.error(f"Database error update_professional_endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database Error update_professional_endpoint: {str(e)}")
    except HTTPException as http_exc:
        logger.error(f"HTTP Exception while updating professional: {str(http_exc.detail)}")
        raise http_exc
    except Exception as e:
        logger.error(f"Error updating professional: {str(e)}")
        raise HTTPException(status_code=500, detail="Error occurred while updating professional: {}".format(str(e)))


# Get professional by ID
@router.get("/{professional_id}", response_model=Professional)
def get_professional_endpoint(professional_id: str):
    '''
    Retrieve a professional by their unique ID.

    Parameters
    ----------
    professional_id : str
        The professional's unique identifier.

    Returns
    -------
    Professional
        The professional object.

    Raises
    ------
    HTTPException
        If professional is not found.
    '''
    try:        
        professional = get_professional_by_id(professional_id)
        if professional is None:
            raise HTTPException(status_code=404, detail="Professional not found")
        return professional
    except psycopg2.Error as e:
        logger.error(f"Database error get_professional_endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database Error get_professional_endpoint: {str(e)}")
    except HTTPException as http_exc:
        logger.error(f"HTTP Exception while fetching professional {professional_id}: {str(http_exc.detail)}")
        raise http_exc
    except Exception as e:
        logger.error(f"Error fetching professional {professional_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Error occurred while fetching professional: {}".format(str(e)))


# Fetch all professionals
@router.get("/", response_model=Professionals)
def get_all_professionals_endpoint():
    '''
    Fetch all professionals from the database.

    Returns
    -------
    Professionals
        A list of all professional objects.

    Raises
    ------
    HTTPException
        If fetching professionals fails.
    '''
    try:
        return fetch_all_professionals()
    except psycopg2.Error as e:
        logger.error(f"Database error get_all_professionals_endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database Error get_all_professionals_endpoint: {str(e)}")
    except HTTPException as http_exc:
        logger.error(f"HTTP Exception while fetching all professionals: {str(http_exc.detail)}")
        raise http_exc
    except Exception as e:
        logger.error(f"Error fetching all professionals: {str(e)}")
        raise HTTPException(status_code=500, detail="Error occurred while fetching professionals: {}".format(str(e)))


# Get professionals with pagination
@router.get("/page/{page}", response_model=Professionals)
def get_professionals_pagination_endpoint(page: int, chunk_size: int = 10, filters: ProfessionalFilters | None = None):
    '''
    Retrieve professionals with pagination.

    Parameters
    ----------
    page : int
        The page number.
    chunk_size : int, optional
        Number of professionals per page. Defaults to 10.
    filters : ProfessionalFilters, optional
        Filters to apply.

    Returns
    -------
    Professionals
        Paginated list of professionals.

    Raises
    ------
    HTTPException
        If fetching professionals fails.
    '''
    try:
        professionals = get_professionals_page(page=page, chunk_size=chunk_size, filters=filters)
        return professionals
    except psycopg2.Error as e:
        logger.error(f"Database error get_professionals_pagination_endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database Error get_professionals_pagination_endpoint: {str(e)}")
    except HTTPException as http_exc:
        logger.error(f"HTTP Exception while fetching professionals page: {str(http_exc.detail)}")
        raise http_exc
    except Exception as e:
        logger.error(f"Error fetching professionals page: {str(e)}")
        raise HTTPException(status_code=500, detail="Error occurred while fetching professionals page: {}".format(str(e)))


# Delete a professional by their unique ID
@router.delete("/{professional_id}", response_model=bool)
def delete_professional_endpoint(professional_id: str):
    '''
    Delete a professional by their unique ID.

    Parameters
    ----------
    professional_id : str
        The professional's unique identifier.

    Returns
    -------
    bool
        True if deletion is successful.

    Raises
    ------
    HTTPException
        If professional is not found or deletion fails.
    '''
    try:
        deleted_professional = delete_professional(professional_id)
        if not deleted_professional:
            raise HTTPException(status_code=404, detail="Professional not found")
        return True
    except psycopg2.Error as e:
        logger.error(f"Database error delete_professional_endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database Error delete_professional_endpoint: {str(e)}")
    except HTTPException as http_exc:
        logger.error(f"HTTP Exception while deleting professional {professional_id}: {str(http_exc.detail)}")
        raise http_exc
    except Exception as e:
        logger.error(f"Error deleting professional {professional_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Error occurred while deleting professional: {}".format(str(e)))
