# services/pdf_extraction_service.py
"""
PDF Extraction Service using existing PyPDF2 dependency.
This service handles PDF text extraction and document processing.
"""

import os
import logging
from typing import List, Dict, Any, Optional
import PyPDF2
import re
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PDFExtractionService:
    """Service for extracting text from PDF files using existing PyPDF2"""
    
    def __init__(self):
        pass
    
    def extract_text_from_pdf(self, file_path: str) -> Dict[str, Any]:
        """
        Extract text from PDF file using PyPDF2
        
        Args:
            file_path: Path to PDF file
            
        Returns:
            Dictionary with extracted text and metadata
        """
        try:
            extracted_data = {
                "text": "",
                "pages": [],
                "metadata": {
                    "source_file": os.path.basename(file_path),
                    "total_pages": 0,
                    "processed_at": datetime.now().isoformat(),
                    "extraction_method": "PyPDF2"
                }
            }
            
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                total_pages = len(pdf_reader.pages)
                extracted_data["metadata"]["total_pages"] = total_pages
                
                full_text = ""
                
                for page_num, page in enumerate(pdf_reader.pages, 1):
                    try:
                        page_text = page.extract_text()
                        
                        if page_text.strip():
                            # Clean up the text
                            cleaned_text = self._clean_text(page_text)
                            
                            page_data = {
                                "page_number": page_num,
                                "text": cleaned_text,
                                "char_count": len(cleaned_text)
                            }
                            
                            extracted_data["pages"].append(page_data)
                            full_text += f"\n\n--- Page {page_num} ---\n\n{cleaned_text}"
                            
                    except Exception as e:
                        logger.warning(f"Error extracting text from page {page_num}: {str(e)}")
                        continue
                
                extracted_data["text"] = full_text.strip()
                
                if not extracted_data["text"]:
                    raise ValueError("No text could be extracted from the PDF")
                
                logger.info(f"Successfully extracted text from {total_pages} pages")
                return extracted_data
                
        except Exception as e:
            logger.error(f"Error extracting text from PDF {file_path}: {str(e)}")
            raise
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize extracted text"""
        if not text:
            return ""
        
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove common PDF artifacts
        text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\xff]', '', text)
        
        # Fix common encoding issues
        text = text.replace('â€™', "'")
        text = text.replace('â€œ', '"')
        text = text.replace('â€', '"')
        text = text.replace('â€¢', '•')
        
        # Normalize line breaks
        text = re.sub(r'\n\s*\n', '\n\n', text)
        
        return text.strip()
    



# Service factory function
def get_pdf_extraction_service() -> PDFExtractionService:
    """Get PDF extraction service instance"""
    return PDFExtractionService()
