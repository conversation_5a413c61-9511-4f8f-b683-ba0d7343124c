# auth_controller.py
"""
Authentication controller with HTTP request handling for authentication operations.
Handles login, token refresh, logout, and user profile retrieval.
"""

import logging
from typing import Dict, Any
from fastapi import HTTPException

from services.auth_service import auth_service

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


def login_controller(msal_token: str) -> Dict[str, Any]:
    """
    Controller for user login with MSAL token exchange.

    Args:
        msal_token: Microsoft MSAL token

    Returns:
        Dict[str, Any]: Authentication result with SmartHR JWT token

    Raises:
        HTTPException: If login fails
    """
    try:
        logger.info("Processing login request")
        
        if not msal_token or not msal_token.strip():
            raise HTTPException(status_code=400, detail="MSAL token is required")
        
        result = auth_service.authenticate_with_msal_token(msal_token.strip())
        logger.info(f"Login successful for user: {result['user']['email']}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in login controller: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def refresh_token_controller(current_token: str) -> Dict[str, Any]:
    """
    Controller for token refresh.

    Args:
        current_token: Current JWT token

    Returns:
        Dict[str, Any]: New token information

    Raises:
        HTTPException: If token refresh fails
    """
    try:
        logger.info("Processing token refresh request")
        
        if not current_token or not current_token.strip():
            raise HTTPException(status_code=400, detail="Current token is required")
        
        result = auth_service.refresh_token(current_token.strip())
        logger.info("Token refresh successful")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in refresh token controller: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def logout_controller(token: str) -> Dict[str, str]:
    """
    Controller for user logout.

    Args:
        token: JWT token

    Returns:
        Dict[str, str]: Logout confirmation

    Raises:
        HTTPException: If logout fails
    """
    try:
        logger.info("Processing logout request")
        
        if not token or not token.strip():
            raise HTTPException(status_code=400, detail="Token is required")
        
        result = auth_service.logout(token.strip())
        logger.info("Logout successful")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in logout controller: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def get_user_profile_controller(token: str) -> Dict[str, Any]:
    """
    Controller for retrieving user profile.

    Args:
        token: JWT token

    Returns:
        Dict[str, Any]: User profile information

    Raises:
        HTTPException: If profile retrieval fails
    """
    try:
        logger.info("Processing user profile request")
        
        if not token or not token.strip():
            raise HTTPException(status_code=400, detail="Token is required")
        
        result = auth_service.get_user_profile(token.strip())
        logger.info(f"Profile retrieved for user: {result['email']}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in get user profile controller: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def verify_user_exists_controller(email: str) -> Dict[str, Any]:
    """
    Controller for verifying if a user exists in SmartHR system.

    Args:
        email: User email address

    Returns:
        Dict[str, Any]: User existence information

    Raises:
        HTTPException: If verification fails
    """
    try:
        logger.info(f"Processing user verification request for: {email}")
        
        if not email or not email.strip():
            raise HTTPException(status_code=400, detail="Email is required")
        
        # Basic email format validation
        if "@" not in email or "." not in email:
            raise HTTPException(status_code=400, detail="Invalid email format")
        
        result = auth_service.verify_user_exists(email.strip().lower())
        logger.info(f"User verification completed for: {email}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in verify user exists controller: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def validate_permission_controller(token: str, permission: str) -> Dict[str, Any]:
    """
    Controller for validating user permission.

    Args:
        token: JWT token
        permission: Permission name to check

    Returns:
        Dict[str, Any]: Permission validation result

    Raises:
        HTTPException: If validation fails
    """
    try:
        logger.info(f"Processing permission validation request for: {permission}")
        
        if not token or not token.strip():
            raise HTTPException(status_code=400, detail="Token is required")
        
        if not permission or not permission.strip():
            raise HTTPException(status_code=400, detail="Permission is required")
        
        has_permission = auth_service.validate_user_permission(token.strip(), permission.strip())
        
        result = {
            "permission": permission,
            "has_permission": has_permission,
            "validated_at": auth_service.decode_smarthr_jwt_token(token).get("auth_time")
        }
        
        logger.info(f"Permission validation completed: {permission} = {has_permission}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in validate permission controller: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def health_check_controller() -> Dict[str, str]:
    """
    Controller for authentication service health check.

    Returns:
        Dict[str, str]: Health status

    Raises:
        HTTPException: If health check fails
    """
    try:
        logger.info("Processing authentication health check")
        
        # Basic health check - could be expanded to check dependencies
        return {
            "status": "healthy",
            "service": "authentication",
            "timestamp": auth_service.decode_smarthr_jwt_token.__module__,  # Just to test import
            "message": "Authentication service is operational"
        }
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail="Authentication service unavailable")


def get_permissions_controller(token: str) -> Dict[str, Any]:
    """
    Controller for retrieving user permissions from token.

    Args:
        token: JWT token

    Returns:
        Dict[str, Any]: User permissions information

    Raises:
        HTTPException: If retrieval fails
    """
    try:
        logger.info("Processing user permissions request")
        
        if not token or not token.strip():
            raise HTTPException(status_code=400, detail="Token is required")
        
        profile = auth_service.get_user_profile(token.strip())
        
        result = {
            "user_email": profile["email"],
            "role": profile["role"],
            "permissions": profile["permissions"],
            "permission_count": len(profile["permissions"]),
            "retrieved_at": profile["auth_time"]
        }
        
        logger.info(f"Permissions retrieved for user: {profile['email']}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in get permissions controller: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def validate_token_controller(token: str) -> Dict[str, Any]:
    """
    Controller for validating JWT token.

    Args:
        token: JWT token to validate

    Returns:
        Dict[str, Any]: Token validation result

    Raises:
        HTTPException: If validation fails
    """
    try:
        logger.info("Processing token validation request")
        
        if not token or not token.strip():
            raise HTTPException(status_code=400, detail="Token is required")
        
        try:
            payload = auth_service.decode_smarthr_jwt_token(token.strip())
            
            result = {
                "valid": True,
                "user_email": payload.get("sub"),
                "role": payload.get("role"),
                "expires_at": payload.get("exp"),
                "issued_at": payload.get("iat"),
                "validated_at": auth_service.decode_smarthr_jwt_token.__module__
            }
            
            logger.info(f"Token validation successful for user: {payload.get('sub')}")
            return result
            
        except Exception:
            result = {
                "valid": False,
                "error": "Invalid or expired token",
                "validated_at": auth_service.decode_smarthr_jwt_token.__module__
            }
            
            logger.warning("Token validation failed")
            return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in validate token controller: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
