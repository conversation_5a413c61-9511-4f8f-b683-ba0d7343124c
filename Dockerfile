# ============================================================================
# STAGE 1: Builder - Install dependencies with build tools
# ============================================================================
FROM python:3.11-slim-bullseye AS builder

WORKDIR /build

# Install build dependencies (these will NOT be in the final image)
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    g++ \
    make \
    libpq-dev \
    build-essential \
    pkg-config \
    python3-dev \
    libcairo2-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy only requirements file first (for better layer caching)
COPY requirements-prod.txt .

# Install Python packages to a local directory
# This allows us to copy only the installed packages to the final image
RUN pip install --no-cache-dir --user -r requirements-prod.txt

# ============================================================================
# STAGE 2: Production - Minimal runtime image
# ============================================================================
FROM python:3.11-slim-bullseye

WORKDIR /app

# Install ONLY runtime dependencies (no build tools)
# Significantly smaller than the builder stage
RUN apt-get update && apt-get install -y --no-install-recommends \
    libpq5 \
    poppler-utils \
    wkhtmltopdf \
    fontconfig \
    fonts-liberation \
    xfonts-75dpi \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/*

# Create a non-root user first (before copying files)
RUN useradd --create-home --no-log-init appuser

# Copy Python packages from builder stage to a location accessible by appuser
# Copy to /usr/local instead of /root/.local for better permissions
COPY --from=builder /root/.local /usr/local

# Copy application code
COPY --chown=appuser:appuser . .

# Verify wkhtmltopdf is installed correctly
RUN wkhtmltopdf -V

# Change ownership of the app directory
RUN chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

EXPOSE 8080

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8080", "--log-level", "info"]


