from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, field_validator, Field
from uuid import UUID
import re


class PermissionBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    category: str = Field(..., min_length=1, max_length=50)
    resource: str = Field(..., min_length=1, max_length=50)
    action: str = Field(..., min_length=1, max_length=50)
    is_active: bool = True


class PermissionCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    category: str = Field(..., min_length=1, max_length=50)
    resource: str = Field(..., min_length=1, max_length=50)
    action: str = Field(..., min_length=1, max_length=50)
    is_active: bool = True

    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('Permission name cannot be empty')
        
        # Check for valid permission name format (alphanumeric, underscores)
        if not re.match(r'^[a-zA-Z0-9_]+$', v.strip()):
            raise ValueError('Permission name can only contain letters, numbers, and underscores')
        
        return v.lower().strip()

    @field_validator('category')
    @classmethod
    def validate_category(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('Category cannot be empty')
        
        valid_categories = ['Candidates', 'Positions', 'Matching', 'Interviews', 'Questions']
        if v.strip() not in valid_categories:
            raise ValueError(f'Category must be one of: {", ".join(valid_categories)}')
        
        return v.strip()

    @field_validator('resource')
    @classmethod
    def validate_resource(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('Resource cannot be empty')
        
        # Check for valid resource name format
        if not re.match(r'^[a-zA-Z0-9_]+$', v.strip()):
            raise ValueError('Resource name can only contain letters, numbers, and underscores')
        
        return v.lower().strip()

    @field_validator('action')
    @classmethod
    def validate_action(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('Action cannot be empty')
        
        # Check for valid action name format
        if not re.match(r'^[a-zA-Z0-9_]+$', v.strip()):
            raise ValueError('Action name can only contain letters, numbers, and underscores')
        
        return v.lower().strip()

    @field_validator('description')
    @classmethod
    def validate_description(cls, v):
        if v is not None and len(v.strip()) > 500:
            raise ValueError('Description cannot exceed 500 characters')
        return v.strip() if v else v


class PermissionUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = None
    category: Optional[str] = Field(None, min_length=1, max_length=50)
    resource: Optional[str] = Field(None, min_length=1, max_length=50)
    action: Optional[str] = Field(None, min_length=1, max_length=50)
    is_active: Optional[bool] = None

    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        if v is not None:
            if not v or len(v.strip()) == 0:
                raise ValueError('Permission name cannot be empty')
            
            if not re.match(r'^[a-zA-Z0-9_]+$', v.strip()):
                raise ValueError('Permission name can only contain letters, numbers, and underscores')
            
            return v.lower().strip()
        return v

    @field_validator('category')
    @classmethod
    def validate_category(cls, v):
        if v is not None:
            if not v or len(v.strip()) == 0:
                raise ValueError('Category cannot be empty')
            
            valid_categories = ['Candidates', 'Positions', 'Matching', 'Interviews', 'Questions']
            if v.strip() not in valid_categories:
                raise ValueError(f'Category must be one of: {", ".join(valid_categories)}')
            
            return v.strip()
        return v

    @field_validator('resource')
    @classmethod
    def validate_resource(cls, v):
        if v is not None:
            if not v or len(v.strip()) == 0:
                raise ValueError('Resource cannot be empty')
            
            if not re.match(r'^[a-zA-Z0-9_]+$', v.strip()):
                raise ValueError('Resource name can only contain letters, numbers, and underscores')
            
            return v.lower().strip()
        return v

    @field_validator('action')
    @classmethod
    def validate_action(cls, v):
        if v is not None:
            if not v or len(v.strip()) == 0:
                raise ValueError('Action cannot be empty')
            
            if not re.match(r'^[a-zA-Z0-9_]+$', v.strip()):
                raise ValueError('Action name can only contain letters, numbers, and underscores')
            
            return v.lower().strip()
        return v

    @field_validator('description')
    @classmethod
    def validate_description(cls, v):
        if v is not None and len(v.strip()) > 500:
            raise ValueError('Description cannot exceed 500 characters')
        return v.strip() if v else v


class PermissionResponse(PermissionBase):
    id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
        validate_by_name = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None,
            UUID: lambda v: str(v) if v else None
        }


class Permission(PermissionBase):
    id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
        validate_by_name = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None,
            UUID: lambda v: str(v) if v else None
        }


class PermissionListResponse(BaseModel):
    permissions: List[PermissionResponse]
    total: int
    page: int
    page_size: int
    total_pages: int

    class Config:
        from_attributes = True


class PermissionFilters(BaseModel):
    name: Optional[str] = None
    category: Optional[str] = None
    resource: Optional[str] = None
    action: Optional[str] = None
    is_active: Optional[bool] = None
    page: int = Field(default=1, ge=1)
    page_size: int = Field(default=10, ge=1, le=100)

    @field_validator('category')
    @classmethod
    def validate_category_filter(cls, v):
        if v is not None:
            valid_categories = ['Candidates', 'Positions', 'Matching', 'Interviews', 'Questions']
            if v.strip() not in valid_categories:
                raise ValueError(f'Category must be one of: {", ".join(valid_categories)}')
            return v.strip()
        return v


class RolePermission(BaseModel):
    """Model for role-permission relationship"""
    id: UUID
    role_id: UUID
    permission_id: UUID
    granted_at: datetime
    granted_by: Optional[str] = None

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None,
            UUID: lambda v: str(v) if v else None
        }


class RolePermissionAssignment(BaseModel):
    """Model for assigning permissions to roles"""
    role_id: UUID
    permission_ids: List[UUID] = Field(..., min_items=1)
    granted_by: str = Field(..., min_length=1, max_length=255)

    @field_validator('permission_ids')
    @classmethod
    def validate_permission_ids(cls, v):
        if not v or len(v) == 0:
            raise ValueError('At least one permission ID is required')
        
        # Check for duplicates
        if len(v) != len(set(v)):
            raise ValueError('Duplicate permission IDs are not allowed')
        
        return v


class UserPermissionCheck(BaseModel):
    """Model for checking user permissions"""
    user_id: UUID
    permission_name: str = Field(..., min_length=1, max_length=100)

    @field_validator('permission_name')
    @classmethod
    def validate_permission_name(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('Permission name cannot be empty')
        
        if not re.match(r'^[a-zA-Z0-9_]+$', v.strip()):
            raise ValueError('Permission name can only contain letters, numbers, and underscores')
        
        return v.lower().strip()


class PermissionCheckResult(BaseModel):
    """Model for permission check results"""
    user_id: UUID
    permission_name: str
    has_permission: bool
    role_name: Optional[str] = None
    checked_at: datetime

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None,
            UUID: lambda v: str(v) if v else None
        }


class PermissionsByCategory(BaseModel):
    """Model for permissions grouped by category"""
    category: str
    permissions: List[PermissionResponse]

    class Config:
        from_attributes = True


# Additional validation functions
def validate_permission_name_format(name: str) -> bool:
    """
    Validate permission name format.
    
    Args:
        name: Permission name to validate
        
    Returns:
        bool: True if name format is valid
    """
    if not name or len(name.strip()) == 0:
        return False
    if len(name.strip()) > 100:
        return False
    # Allow letters, numbers, underscores
    return re.match(r'^[a-zA-Z0-9_]+$', name.strip()) is not None


def validate_permission_category(category: str) -> bool:
    """
    Validate permission category.
    
    Args:
        category: Category to validate
        
    Returns:
        bool: True if category is valid
    """
    valid_categories = ['Candidates', 'Positions', 'Matching', 'Interviews', 'Questions']
    return category.strip() in valid_categories


def sanitize_permission_input(value: str) -> str:
    """
    Sanitize permission input by removing potentially harmful characters.
    
    Args:
        value: Input value to sanitize
        
    Returns:
        str: Sanitized value
    """
    if not value:
        return value
    
    # Remove potential SQL injection characters and XSS attempts
    dangerous_chars = ['<', '>', '"', "'", '&', ';', '--', '/*', '*/', 'script']
    sanitized = value
    
    for char in dangerous_chars:
        sanitized = sanitized.replace(char, '')
    
    return sanitized.strip()


class PermissionValidationError(Exception):
    """Custom exception for permission validation errors"""
    pass


def validate_permission_data_integrity(permission_data: dict) -> tuple[bool, List[str]]:
    """
    Comprehensive validation of permission data integrity.
    
    Args:
        permission_data: Dictionary containing permission data
        
    Returns:
        tuple: (is_valid, list_of_errors)
    """
    errors = []
    
    # Check required fields
    required_fields = ['name', 'category', 'resource', 'action']
    for field in required_fields:
        if field not in permission_data or not permission_data[field]:
            errors.append(f"{field.capitalize()} is required")
    
    # Validate name format
    if 'name' in permission_data and permission_data['name']:
        if not validate_permission_name_format(permission_data['name']):
            errors.append("Invalid permission name format")
    
    # Validate category
    if 'category' in permission_data and permission_data['category']:
        if not validate_permission_category(permission_data['category']):
            errors.append("Invalid permission category")
    
    # Validate resource format
    if 'resource' in permission_data and permission_data['resource']:
        if not re.match(r'^[a-zA-Z0-9_]+$', permission_data['resource']):
            errors.append("Invalid resource format")
    
    # Validate action format
    if 'action' in permission_data and permission_data['action']:
        if not re.match(r'^[a-zA-Z0-9_]+$', permission_data['action']):
            errors.append("Invalid action format")
    
    return len(errors) == 0, errors
