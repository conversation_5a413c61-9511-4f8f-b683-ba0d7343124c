version: '3.8'

services:
  # Optimized SmartHR Backend Service
  smarthr-backend:
    build:
      context: .
      dockerfile: Dockerfile
      # Use BuildKit for better caching and performance
      cache_from:
        - smarthr-backend:optimized
    image: smarthr-backend:optimized
    container_name: smarthr-backend
    ports:
      - "8080:8080"
    environment:
      # Database configuration
      - DATABASE_URL=${DATABASE_URL}
      
      # API Keys
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - GROQ_API_KEY=${GROQ_API_KEY}
      
      # Lumus API
      - LUMUS_API_URL=${LUMUS_API_URL}
      - LUMUS_API_TIMEOUT=${LUMUS_API_TIMEOUT:-320.0}
      
      # Azure Monitor (optional)
      - APPLICATIONINSIGHTS_CONNECTION_STRING=${APPLICATIONINSIGHTS_CONNECTION_STRING:-}
      
      # Environment
      - ENVIRONMENT=${ENVIRONMENT:-production}
      - LOG_LEVEL=${LOG_LEVEL:-info}
    
    # Resource limits (adjust based on your needs)
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M
    
    # Health check
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8080/health')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Restart policy
    restart: unless-stopped
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    
    # Networks
    networks:
      - smarthr-network

  # PostgreSQL Database (optional - for local development)
  postgres:
    image: postgres:15-alpine
    container_name: smarthr-postgres
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-smarthr}
      - POSTGRES_USER=${POSTGRES_USER:-smarthr}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - smarthr-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-smarthr}"]
      interval: 10s
      timeout: 5s
      retries: 5

networks:
  smarthr-network:
    driver: bridge

volumes:
  postgres-data:
    driver: local
