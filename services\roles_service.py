# roles_service.py
"""
Service layer for role CRUD operations.
Handles business logic and database interactions for role management.
"""

from contextlib import contextmanager
from typing import List, Optional, Dict, Any
from datetime import datetime
import logging
from uuid import UUID

import psycopg2
from psycopg2 import sql
from psycopg2.extras import RealDictCursor
from fastapi import HTTPException

from core.config import settings
from models.role import (
    Role,
    RoleCreate,
    RoleUpdate,
    RoleResponse,
    RoleFilters,
    RoleListResponse,
    RoleHierarchy,
    RoleAssignment,
    validate_role_data_integrity
)

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


# Database connection context manager
@contextmanager
def get_cursor():
    """
    Context manager for PostgreSQL cursor with error handling.

    Yields:
        cursor: PostgreSQL cursor with RealDictCursor factory

    Raises:
        HTTPException: If database connection fails
    """
    conn = None
    try:
        conn = psycopg2.connect(
            settings.DATABASE_URL,
            connect_timeout=120,
            options="-c statement_timeout=120000",
            keepalives_idle=30
        )
        with conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                yield cur
    except psycopg2.Error as e:
        logger.error(f"Database connection error: {e}")
        raise HTTPException(
            status_code=500, detail="Database connection failed")
    finally:
        if conn:
            conn.close()


def create_role(role_data: RoleCreate) -> RoleResponse:
    """
    Create a new role in the database.

    Args:
        role_data: Role creation data

    Returns:
        RoleResponse: Created role data

    Raises:
        HTTPException: If role creation fails or name already exists
    """
    try:
        # Validate role data integrity
        is_valid, errors = validate_role_data_integrity(role_data.model_dump())
        if not is_valid:
            raise HTTPException(
                status_code=400, detail=f"Validation errors: {', '.join(errors)}")

        # Validate system role constraints
        if role_data.is_system_role:
            role_data.validate_system_role_name()

        with get_cursor() as cur:
            # Check if role with name already exists
            cur.execute(
                "SELECT id FROM roles_smarthr WHERE name = %s",
                (role_data.name,)
            )
            if cur.fetchone():
                raise HTTPException(
                    status_code=400,
                    detail=f"Role with name {role_data.name} already exists"
                )

            # Validate parent role exists if specified
            if role_data.parent_role_id:
                cur.execute(
                    "SELECT hierarchy_level FROM roles_smarthr WHERE id = %s AND is_active = TRUE",
                    (str(role_data.parent_role_id),)
                )
                parent_result = cur.fetchone()
                if not parent_result:
                    raise HTTPException(
                        status_code=400, detail="Parent role not found or inactive")

                # Validate hierarchy level
                if role_data.hierarchy_level <= parent_result['hierarchy_level']:
                    raise HTTPException(
                        status_code=400,
                        detail="Child role hierarchy level must be greater than parent role level"
                    )

            # Insert new role
            insert_query = sql.SQL("""
                INSERT INTO roles_smarthr (name, description, is_system_role, hierarchy_level, parent_role_id, is_active)
                VALUES (%s, %s, %s, %s, %s, %s)
                RETURNING id, name, description, is_system_role, hierarchy_level, parent_role_id, 
                         is_active, created_at, updated_at
            """)

            cur.execute(insert_query, (
                role_data.name,
                role_data.description,
                role_data.is_system_role,
                role_data.hierarchy_level,
                role_data.parent_role_id,
                role_data.is_active
            ))

            result = cur.fetchone()
            if not result:
                raise HTTPException(
                    status_code=500, detail="Failed to create role")

            logger.info(f"Role created successfully: {result['name']}")
            return RoleResponse(**dict(result))

    except psycopg2.Error as e:
        logger.error(f"Database error creating role: {e}")
        raise HTTPException(status_code=500, detail="Failed to create role")


def get_role_by_id(role_id: UUID) -> Optional[RoleResponse]:
    """
    Retrieve a role by ID.

    Args:
        role_id: Role UUID

    Returns:
        RoleResponse: Role data if found, None otherwise
    """
    try:
        with get_cursor() as cur:
            cur.execute("""
                SELECT id, name, description, is_system_role, hierarchy_level, parent_role_id,
                       is_active, created_at, updated_at
                FROM roles_smarthr 
                WHERE id = %s
            """, (str(role_id),))

            result = cur.fetchone()
            if result:
                return RoleResponse(**dict(result))
            return None

    except psycopg2.Error as e:
        logger.error(f"Database error retrieving role by ID: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve role")


def get_role_by_name(name: str) -> Optional[RoleResponse]:
    """
    Retrieve a role by name.

    Args:
        name: Role name

    Returns:
        RoleResponse: Role data if found, None otherwise
    """
    try:
        with get_cursor() as cur:
            cur.execute("""
                SELECT id, name, description, is_system_role, hierarchy_level, parent_role_id,
                       is_active, created_at, updated_at
                FROM roles_smarthr 
                WHERE name = %s
            """, (name.lower().strip(),))

            result = cur.fetchone()
            if result:
                return RoleResponse(**dict(result))
            return None

    except psycopg2.Error as e:
        logger.error(f"Database error retrieving role by name: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve role")


def update_role(role_id: UUID, role_data: RoleUpdate) -> RoleResponse:
    """
    Update an existing role.

    Args:
        role_id: Role UUID
        role_data: Role update data

    Returns:
        RoleResponse: Updated role data

    Raises:
        HTTPException: If role not found or update fails
    """
    try:
        with get_cursor() as cur:
            # Check if role exists
            cur.execute(
                "SELECT id, is_system_role FROM roles_smarthr WHERE id = %s", (str(role_id),))
            existing_role = cur.fetchone()
            if not existing_role:
                raise HTTPException(status_code=404, detail="Role not found")

            # Prevent modification of system roles (except activation/deactivation)
            if existing_role['is_system_role'] and (role_data.name or role_data.hierarchy_level is not None):
                raise HTTPException(
                    status_code=400, detail="Cannot modify system role properties")

            # Build dynamic update query
            update_fields = []
            update_values = []

            if role_data.name is not None:
                # Check for name conflicts
                cur.execute(
                    "SELECT id FROM roles_smarthr WHERE name = %s AND id != %s",
                    (role_data.name, str(role_id))
                )
                if cur.fetchone():
                    raise HTTPException(
                        status_code=400, detail="Role name already exists")

                update_fields.append("name = %s")
                update_values.append(role_data.name)

            if role_data.description is not None:
                update_fields.append("description = %s")
                update_values.append(role_data.description)

            if role_data.hierarchy_level is not None:
                update_fields.append("hierarchy_level = %s")
                update_values.append(role_data.hierarchy_level)

            if role_data.parent_role_id is not None:
                # Validate parent role exists
                cur.execute(
                    "SELECT hierarchy_level FROM roles_smarthr WHERE id = %s AND is_active = TRUE",
                    (str(role_data.parent_role_id),)
                )
                parent_result = cur.fetchone()
                if not parent_result:
                    raise HTTPException(
                        status_code=400, detail="Parent role not found or inactive")

                update_fields.append("parent_role_id = %s")
                update_values.append(role_data.parent_role_id)

            if role_data.is_active is not None:
                update_fields.append("is_active = %s")
                update_values.append(role_data.is_active)

            update_fields.append("updated_at = NOW()")

            if not update_fields:
                raise HTTPException(
                    status_code=400, detail="No fields to update")

            update_values.append(str(role_id))

            update_query = f"""
                UPDATE roles_smarthr 
                SET {', '.join(update_fields)}
                WHERE id = %s
                RETURNING id, name, description, is_system_role, hierarchy_level, parent_role_id,
                         is_active, created_at, updated_at
            """

            cur.execute(update_query, update_values)
            result = cur.fetchone()

            if not result:
                raise HTTPException(
                    status_code=500, detail="Failed to update role")

            logger.info(f"Role updated successfully: {result['name']}")
            return RoleResponse(**dict(result))

    except psycopg2.Error as e:
        logger.error(f"Database error updating role: {e}")
        raise HTTPException(status_code=500, detail="Failed to update role")


def delete_role(role_id: UUID) -> bool:
    """
    Delete a role (soft delete by setting is_active to False).

    Args:
        role_id: Role UUID

    Returns:
        bool: True if role was deleted successfully

    Raises:
        HTTPException: If role not found, is system role, or deletion fails
    """
    try:
        with get_cursor() as cur:
            # Check if role exists and is not a system role
            cur.execute("""
                SELECT id, is_system_role, name FROM roles_smarthr 
                WHERE id = %s AND is_active = TRUE
            """, (str(role_id),))

            role = cur.fetchone()
            if not role:
                raise HTTPException(
                    status_code=404, detail="Role not found or already inactive")

            if role['is_system_role']:
                raise HTTPException(
                    status_code=400, detail="Cannot delete system roles")

            # Check if role is assigned to any users
            cur.execute(
                "SELECT COUNT(*) as count FROM users_smarthr WHERE role_id = %s", (str(role_id),))
            user_count = cur.fetchone()['count']
            if user_count > 0:
                raise HTTPException(
                    status_code=400,
                    detail=f"Cannot delete role. It is assigned to {user_count} user(s)"
                )

            # Soft delete the role
            cur.execute("""
                UPDATE roles_smarthr 
                SET is_active = FALSE, updated_at = NOW()
                WHERE id = %s
            """, (str(role_id),))

            if cur.rowcount == 0:
                raise HTTPException(status_code=404, detail="Role not found")

            logger.info(f"Role deactivated successfully: {role['name']}")
            return True

    except psycopg2.Error as e:
        logger.error(f"Database error deleting role: {e}")
        raise HTTPException(status_code=500, detail="Failed to delete role")


def list_roles(filters: RoleFilters) -> RoleListResponse:
    """
    List roles with filtering and pagination.

    Args:
        filters: Role filtering and pagination parameters

    Returns:
        RoleListResponse: Paginated list of roles
    """
    try:
        with get_cursor() as cur:
            # Build WHERE clause
            where_conditions = []
            where_values = []

            if filters.name:
                where_conditions.append("name ILIKE %s")
                where_values.append(f"%{filters.name}%")

            if filters.is_system_role is not None:
                where_conditions.append("is_system_role = %s")
                where_values.append(filters.is_system_role)

            if filters.hierarchy_level is not None:
                where_conditions.append("hierarchy_level = %s")
                where_values.append(filters.hierarchy_level)

            if filters.parent_role_id is not None:
                where_conditions.append("parent_role_id = %s")
                where_values.append(str(filters.parent_role_id))

            if filters.is_active is not None:
                where_conditions.append("is_active = %s")
                where_values.append(filters.is_active)

            where_clause = "WHERE " + \
                " AND ".join(where_conditions) if where_conditions else ""

            # Get total count
            count_query = f"SELECT COUNT(*) as total FROM roles_smarthr {where_clause}"
            cur.execute(count_query, where_values)
            total = cur.fetchone()['total']

            # Calculate pagination
            offset = (filters.page - 1) * filters.page_size
            total_pages = (total + filters.page_size - 1) // filters.page_size

            # Get paginated results
            list_query = f"""
                SELECT id, name, description, is_system_role, hierarchy_level, parent_role_id,
                       is_active, created_at, updated_at
                FROM roles_smarthr
                {where_clause}
                ORDER BY hierarchy_level ASC, name ASC
                LIMIT %s OFFSET %s
            """

            cur.execute(list_query, where_values + [filters.page_size, offset])
            results = cur.fetchall()

            roles = [RoleResponse(**dict(row)) for row in results]

            return RoleListResponse(
                roles=roles,
                total=total,
                page=filters.page,
                page_size=filters.page_size,
                total_pages=total_pages
            )

    except psycopg2.Error as e:
        logger.error(f"Database error listing roles: {e}")
        raise HTTPException(status_code=500, detail="Failed to list roles")


def get_role_hierarchy() -> List[RoleHierarchy]:
    """
    Get complete role hierarchy tree.

    Returns:
        List[RoleHierarchy]: Hierarchical role structure
    """
    try:
        with get_cursor() as cur:
            # Get all active roles
            cur.execute("""
                SELECT id, name, description, hierarchy_level, parent_role_id, is_system_role, is_active
                FROM roles_smarthr
                WHERE is_active = TRUE
                ORDER BY hierarchy_level ASC, name ASC
            """)

            roles = cur.fetchall()
            role_dict = {}
            root_roles = []

            # Create role objects
            for role in roles:
                role_obj = RoleHierarchy(
                    id=role['id'],
                    name=role['name'],
                    description=role['description'],
                    hierarchy_level=role['hierarchy_level'],
                    parent_role_id=role['parent_role_id'],
                    is_system_role=role['is_system_role'],
                    is_active=role['is_active'],
                    children=[]
                )
                role_dict[role['id']] = role_obj

                if not role['parent_role_id']:
                    root_roles.append(role_obj)

            # Build hierarchy
            for role in roles:
                if role['parent_role_id'] and role['parent_role_id'] in role_dict:
                    parent = role_dict[role['parent_role_id']]
                    child = role_dict[role['id']]
                    parent.children.append(child)

            return root_roles

    except psycopg2.Error as e:
        logger.error(f"Database error getting role hierarchy: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to get role hierarchy")


def assign_role_to_user(user_id: UUID, role_id: UUID, assigned_by: str) -> bool:
    """
    Assign a role to a user.

    Args:
        user_id: User UUID
        role_id: Role UUID
        assigned_by: Who assigned the role

    Returns:
        bool: True if assignment was successful

    Raises:
        HTTPException: If user or role not found, or assignment fails
    """
    try:
        with get_cursor() as cur:
            # Verify user exists
            cur.execute(
                "SELECT id FROM users_smarthr WHERE id = %s AND is_active = TRUE", (str(user_id),))
            if not cur.fetchone():
                raise HTTPException(
                    status_code=404, detail="User not found or inactive")

            # Verify role exists
            cur.execute(
                "SELECT id FROM roles_smarthr WHERE id = %s AND is_active = TRUE", (str(role_id),))
            if not cur.fetchone():
                raise HTTPException(
                    status_code=404, detail="Role not found or inactive")

            # Update user's role
            cur.execute("""
                UPDATE users_smarthr
                SET role_id = %s, updated_at = NOW(), updated_by = %s
                WHERE id = %s
            """, (str(role_id), assigned_by, str(user_id)))

            if cur.rowcount == 0:
                raise HTTPException(status_code=404, detail="User not found")

            logger.info(
                f"Role {role_id} assigned to user {user_id} by {assigned_by}")
            return True

    except psycopg2.Error as e:
        logger.error(f"Database error assigning role to user: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to assign role to user")


def get_users_by_role(role_id: UUID) -> List[Dict[str, Any]]:
    """
    Get all users assigned to a specific role.

    Args:
        role_id: Role UUID

    Returns:
        List[Dict]: List of users with the role
    """
    try:
        with get_cursor() as cur:
            cur.execute("""
                SELECT u.id, u.email, u.name, u.is_active, u.created_at, u.updated_at
                FROM users_smarthr u
                WHERE u.role_id = %s AND u.is_active = TRUE
                ORDER BY u.name ASC
            """, (str(role_id),))

            results = cur.fetchall()
            return [dict(row) for row in results]

    except psycopg2.Error as e:
        logger.error(f"Database error getting users by role: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to get users by role")


def get_role_permissions(role_id: UUID) -> List[Dict[str, Any]]:
    """
    Get all permissions assigned to a role.

    Args:
        role_id: Role UUID

    Returns:
        List[Dict]: List of permissions for the role
    """
    try:
        with get_cursor() as cur:
            cur.execute("""
                SELECT p.id, p.name, p.description, p.category, p.resource, p.action,
                       rp.granted_at, rp.granted_by
                FROM permissions_smarthr p
                JOIN role_permissions rp ON p.id = rp.permission_id
                WHERE rp.role_id = %s AND p.is_active = TRUE
                ORDER BY p.category ASC, p.name ASC
            """, (str(role_id),))

            results = cur.fetchall()
            return [dict(row) for row in results]

    except psycopg2.Error as e:
        logger.error(f"Database error getting role permissions: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to get role permissions")


def activate_role(role_id: UUID) -> RoleResponse:
    """
    Activate a role (set is_active to True).

    Args:
        role_id: Role UUID

    Returns:
        RoleResponse: Updated role data

    Raises:
        HTTPException: If role not found or activation fails
    """
    try:
        with get_cursor() as cur:
            cur.execute("""
                UPDATE roles_smarthr
                SET is_active = TRUE, updated_at = NOW()
                WHERE id = %s
                RETURNING id, name, description, is_system_role, hierarchy_level, parent_role_id,
                         is_active, created_at, updated_at
            """, (str(role_id),))

            result = cur.fetchone()
            if not result:
                raise HTTPException(status_code=404, detail="Role not found")

            logger.info(f"Role activated successfully: {result['name']}")
            return RoleResponse(**dict(result))

    except psycopg2.Error as e:
        logger.error(f"Database error activating role: {e}")
        raise HTTPException(status_code=500, detail="Failed to activate role")
