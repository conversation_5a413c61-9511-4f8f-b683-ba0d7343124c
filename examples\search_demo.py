"""
Demo script showing how to use the Professional Search Adapter Pattern.
This demonstrates the search functionality across multiple providers.
"""

import asyncio
import json
from typing import List, Optional

# Import the search service directly for demo purposes
from services.search_service import search_service


async def demo_basic_search():
    """Demonstrate basic professional search"""
    print("=== Basic Professional Search Demo ===")

    try:
        # Basic search for QA professionals
        results = await search_service.search_professionals(
            query="QA engineer",
            providers=["linkedin"],
            limit=5,
            created_by="demo_user"
        )

        print(f"Search completed successfully!")
        print(f"Total results: {results['total_results']}")
        print(f"Providers searched: {results['providers_searched']}")

        # Display first professional
        if results['professionals']:
            first_prof = results['professionals'][0]
            prof_info = first_prof['professional_info']

            print(f"\nFirst Professional:")
            print(f"Name: {prof_info.get('personal_info', {}).get('full_name', 'N/A')}")
            print(f"Source: {prof_info.get('source', 'N/A')}")
            print(f"Summary: {prof_info.get('summary', 'N/A')[:100]}...")
            print(f"Tags: {prof_info.get('tags', [])[:5]}")

    except Exception as e:
        print(f"Error in basic search: {str(e)}")


async def demo_filtered_search():
    """Demonstrate search with filters"""
    print("\n=== Filtered Professional Search Demo ===")

    try:
        # Search with filters
        results = await search_service.search_professionals(
            query="full stack developer",
            providers=["linkedin"],
            location="United States",
            skills=["React", "Node.js"],
            experience_level="senior",
            limit=3,
            created_by="demo_user"
        )

        print(f"Filtered search completed!")
        print(f"Total results: {results['total_results']}")

        # Display all professionals
        for i, prof in enumerate(results['professionals'], 1):
            prof_info = prof['professional_info']
            print(f"\nProfessional {i}:")
            print(f"Name: {prof_info.get('personal_info', {}).get('full_name', 'N/A')}")
            print(f"Location: {prof_info.get('personal_info', {}).get('city', 'N/A')}")

            # Show skills
            skills = prof_info.get('skills', [])
            skill_names = [skill.get('name', '') for skill in skills[:3] if isinstance(skill, dict)]
            print(f"Top Skills: {', '.join(skill_names)}")

            # Show latest job
            work_exp = prof_info.get('work_experience', [])
            if work_exp and isinstance(work_exp[0], dict):
                latest_job = work_exp[0]
                print(f"Current Role: {latest_job.get('job_title', 'N/A')} at {latest_job.get('company_name', 'N/A')}")

    except Exception as e:
        print(f"Error in filtered search: {str(e)}")


async def demo_provider_info():
    """Demonstrate getting provider information"""
    print("\n=== Provider Information Demo ===")

    try:
        # Get available providers
        providers = search_service.get_available_providers()
        print(f"Available providers: {providers}")

        # Get detailed info for each provider
        for provider_name in providers:
            info = search_service.get_provider_info(provider_name)
            print(f"\nProvider: {provider_name}")
            print(f"Type: {info['type']}")
            print(f"Available: {info['available']}")

    except Exception as e:
        print(f"Error getting provider info: {str(e)}")


async def demo_tag_generation():
    """Demonstrate tag generation from professional data"""
    print("\n=== Tag Generation Demo ===")

    try:
        # Search for professionals and show tag generation
        results = await search_service.search_professionals(
            query="software engineer",
            providers=["linkedin"],
            limit=2,
            created_by="demo_user"
        )

        print("Tag generation examples:")
        for i, prof in enumerate(results['professionals'], 1):
            prof_info = prof['professional_info']
            name = prof_info.get('personal_info', {}).get('full_name', f'Professional {i}')
            tags = prof_info.get('tags', [])

            print(f"\n{name}:")
            print(f"Generated tags: {tags[:10]}")  # Show first 10 tags

            # Show how tags were derived
            roles = prof_info.get('roles', [])
            if roles:
                print(f"Roles: {roles[:3]}")

            skills = prof_info.get('skills', [])
            if skills:
                skill_names = [skill.get('name', '') for skill in skills[:3] if isinstance(skill, dict)]
                print(f"Skills: {skill_names}")

    except Exception as e:
        print(f"Error in tag generation demo: {str(e)}")


async def demo_adapter_pattern():
    """Demonstrate the Adapter Pattern in action"""
    print("\n=== Adapter Pattern Demo ===")

    try:
        # Get LinkedIn provider directly to show adaptation
        linkedin_provider = search_service.providers.get("linkedin")

        if linkedin_provider:
            print("LinkedIn Provider Adapter Pattern Demo:")

            # Show mock LinkedIn data structure (before adaptation)
            print("\n1. Raw LinkedIn Data Structure (simulated):")
            mock_linkedin_data = {
                "linkedin_id": "demo-user",
                "summary": "Experienced software developer...",
                "skills": [
                    {"name": "Python", "endorsements": 25, "years_experience": 5.0}
                ],
                "personal_info": {
                    "full_name": "Demo User",
                    "email": "<EMAIL>"
                }
            }
            print(json.dumps(mock_linkedin_data, indent=2))

            # Show adapted data structure (after adaptation)
            print("\n2. Adapted Professional Data Structure:")
            adapted_data = linkedin_provider.adapt_to_professional_model(mock_linkedin_data)
            print(json.dumps({
                "source": adapted_data.get("source"),
                "personal_info": adapted_data.get("personal_info"),
                "skills": adapted_data.get("skills", [])[:1],  # Show first skill
                "tags": adapted_data.get("tags", [])[:5],  # Show first 5 tags
                "summary": adapted_data.get("summary", "")[:50] + "..."
            }, indent=2))

            print("\n3. Key Adapter Pattern Features:")
            print("- Source attribution: Each professional has 'source' field")
            print("- Standardized format: All providers return same structure")
            print("- Tag generation: Automatic tag extraction for search")
            print("- Extensible: Easy to add new providers (Indeed, Glassdoor, etc.)")

        else:
            print("LinkedIn provider not available")

    except Exception as e:
        print(f"Error in adapter pattern demo: {str(e)}")


async def main():
    """Run all demos"""
    print("Professional Search Adapter Pattern Demo")
    print("=" * 50)

    await demo_basic_search()
    await demo_filtered_search()
    await demo_provider_info()
    await demo_tag_generation()
    await demo_adapter_pattern()

    print("\n" + "=" * 50)
    print("Demo completed!")
    print("\nTo use in your application:")
    print("1. Import: from services.search_service import search_service")
    print("2. Search: await search_service.search_professionals(query='your query')")
    print("3. Use the API endpoints: GET /recruiter/search/professionals?query=your_query")


if __name__ == "__main__":
    # Run the demo
    asyncio.run(main())
