"""
Simple script to generate a JWT token for testing MCP endpoints.
"""

import os
import sys

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Try to load dotenv, but don't fail if not available
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("⚠️  python-dotenv not installed, using system environment variables")

from utils.mcp_jwt_utils import create_mcp_token

def main():
    """Generate and print a JWT token."""
    
    print("="*60)
    print("JWT Token Generator for MCP Endpoints")
    print("="*60)
    
    # Check if MCP_JWT_SECRET is set
    if not os.getenv("MCP_JWT_SECRET"):
        print("\n❌ Error: MCP_JWT_SECRET not set in .env file")
        print("\nPlease add the following to your .env file:")
        print("MCP_JWT_SECRET=your_secure_jwt_secret_here_at_least_32_characters")
        sys.exit(1)
    
    try:
        # Generate token
        token = create_mcp_token(service_name="test-client")
        
        print("\n✅ JWT Token generated successfully!\n")
        print("Token:")
        print("-" * 60)
        print(token)
        print("-" * 60)
        
        print("\n📋 Use this token in your curl commands:")
        print(f'Authorization: Bearer {token}')
        
        print("\n📝 Example curl command:")
        print(f"""
curl -X POST http://localhost:8080/mcp-linkedin \\
  -H "Authorization: Bearer {token}" \\
  -H "Content-Type: application/json" \\
  -d '{{
    "role": "Python developer",
    "country": "México",
    "num_profiles": 3,
    "scrape_profiles": false
  }}'
""")
        
    except Exception as e:
        print(f"\n❌ Error generating token: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

