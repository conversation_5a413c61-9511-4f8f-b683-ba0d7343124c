import logging
import re
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from urllib.parse import urlparse

from models.linkedin import (
    LinkedInProfile,
    LinkedInSearchResponse,
    LinkedInLocation,
    LinkedInCompany,
    LinkedInExperience,
    LinkedInEducation,
    LinkedInSkill
)

logger = logging.getLogger(__name__)


class LinkedInResponseProcessor:
    """Processor for normalizing and enriching LinkedIn response data."""
    
    def __init__(self):
        self.location_normalizer = LocationNormalizer()
        self.company_normalizer = CompanyNormalizer()
        self.experience_processor = ExperienceProcessor()
        self.education_processor = EducationProcessor()
        self.skill_processor = SkillProcessor()
    
    def process_search_response(self, response: LinkedInSearchResponse) -> LinkedInSearchResponse:
        """Process and normalize entire search response."""
        try:
            processed_profiles = []
            
            for profile in response.profiles:
                try:
                    processed_profile = self.process_profile(profile)
                    processed_profiles.append(processed_profile)
                except Exception as e:
                    logger.warning(f"Failed to process profile {profile.id}: {str(e)}")
                    # Include original profile as fallback
                    processed_profiles.append(profile)
            
            # Update response with processed profiles
            response.profiles = processed_profiles
            
            # Add processing metadata
            response.search_metadata.update({
                "processed_profiles": len(processed_profiles),
                "processing_timestamp": datetime.now().isoformat(),
                "processing_version": "1.0"
            })
            
            logger.info(f"Processed {len(processed_profiles)} profiles in search response")
            return response
            
        except Exception as e:
            logger.error(f"Failed to process search response: {str(e)}")
            return response  # Return original response as fallback
    
    def process_profile(self, profile: LinkedInProfile) -> LinkedInProfile:
        """Process and normalize a single LinkedIn profile."""
        try:
            # Normalize basic information
            processed_profile = profile.copy()
            
            # Process name fields
            processed_profile = self._process_name_fields(processed_profile)
            
            # Process location
            if processed_profile.location:
                processed_profile.location = self.location_normalizer.normalize_location(
                    processed_profile.location
                )
            
            # Process experience
            if processed_profile.experience:
                processed_profile.experience = [
                    self.experience_processor.process_experience(exp)
                    for exp in processed_profile.experience
                ]
            
            # Process education
            if processed_profile.education:
                processed_profile.education = [
                    self.education_processor.process_education(edu)
                    for edu in processed_profile.education
                ]
            
            # Process skills
            if processed_profile.skills:
                processed_profile.skills = self.skill_processor.process_skills(
                    processed_profile.skills
                )
            
            # Generate profile URL if missing
            if not processed_profile.profile_url and processed_profile.public_id:
                processed_profile.profile_url = f"https://www.linkedin.com/in/{processed_profile.public_id}"
            
            # Set last updated timestamp
            processed_profile.last_updated = datetime.now()
            
            return processed_profile
            
        except Exception as e:
            logger.error(f"Failed to process profile {profile.id}: {str(e)}")
            return profile  # Return original profile as fallback
    
    def _process_name_fields(self, profile: LinkedInProfile) -> LinkedInProfile:
        """Process and normalize name fields."""
        # Clean name fields
        if profile.first_name:
            profile.first_name = self._clean_name(profile.first_name)
        
        if profile.last_name:
            profile.last_name = self._clean_name(profile.last_name)
        
        # Generate full name if missing
        if not profile.full_name and profile.first_name and profile.last_name:
            profile.full_name = f"{profile.first_name} {profile.last_name}"
        
        return profile
    
    def _clean_name(self, name: str) -> str:
        """Clean and normalize name strings."""
        if not name:
            return name
        
        # Remove extra whitespace
        name = re.sub(r'\s+', ' ', name.strip())
        
        # Capitalize properly
        name = name.title()
        
        # Handle common name patterns
        name = re.sub(r'\b(Mc|Mac)([a-z])', lambda m: m.group(1) + m.group(2).upper(), name)
        name = re.sub(r'\b(O\')([a-z])', lambda m: m.group(1) + m.group(2).upper(), name)
        
        return name


class LocationNormalizer:
    """Normalizer for LinkedIn location data."""
    
    def __init__(self):
        self.country_codes = self._load_country_codes()
        self.city_mappings = self._load_city_mappings()
    
    def normalize_location(self, location: LinkedInLocation) -> LinkedInLocation:
        """Normalize location information."""
        try:
            normalized_location = location.copy()
            
            # Parse location name
            if location.name:
                parsed_location = self._parse_location_string(location.name)
                
                # Update fields with parsed information
                if not normalized_location.city and parsed_location.get('city'):
                    normalized_location.city = parsed_location['city']
                
                if not normalized_location.region and parsed_location.get('region'):
                    normalized_location.region = parsed_location['region']
                
                if not normalized_location.country_code and parsed_location.get('country_code'):
                    normalized_location.country_code = parsed_location['country_code']
            
            # Normalize country code
            if normalized_location.country_code:
                normalized_location.country_code = normalized_location.country_code.upper()
            
            return normalized_location
            
        except Exception as e:
            logger.warning(f"Failed to normalize location: {str(e)}")
            return location
    
    def _parse_location_string(self, location_str: str) -> Dict[str, str]:
        """Parse location string into components."""
        parsed = {}
        
        # Common patterns: "City, State, Country" or "City, Country"
        parts = [part.strip() for part in location_str.split(',')]
        
        if len(parts) >= 3:
            parsed['city'] = parts[0]
            parsed['region'] = parts[1]
            parsed['country'] = parts[2]
            parsed['country_code'] = self._get_country_code(parts[2])
        elif len(parts) == 2:
            parsed['city'] = parts[0]
            parsed['country'] = parts[1]
            parsed['country_code'] = self._get_country_code(parts[1])
        elif len(parts) == 1:
            # Could be city or country
            if parts[0] in self.country_codes.values():
                parsed['country'] = parts[0]
                parsed['country_code'] = self._get_country_code(parts[0])
            else:
                parsed['city'] = parts[0]
        
        return parsed
    
    def _get_country_code(self, country_name: str) -> Optional[str]:
        """Get country code from country name."""
        country_name = country_name.strip().lower()
        
        for code, name in self.country_codes.items():
            if name.lower() == country_name:
                return code
        
        return None
    
    def _load_country_codes(self) -> Dict[str, str]:
        """Load country code mappings."""
        # Simplified country code mapping
        return {
            "US": "United States",
            "CA": "Canada",
            "GB": "United Kingdom",
            "DE": "Germany",
            "FR": "France",
            "IN": "India",
            "AU": "Australia",
            "BR": "Brazil",
            "JP": "Japan",
            "CN": "China"
        }
    
    def _load_city_mappings(self) -> Dict[str, str]:
        """Load city name mappings for normalization."""
        return {
            "sf": "San Francisco",
            "nyc": "New York City",
            "la": "Los Angeles",
            "dc": "Washington, D.C."
        }


class CompanyNormalizer:
    """Normalizer for LinkedIn company data."""
    
    def normalize_company(self, company: LinkedInCompany) -> LinkedInCompany:
        """Normalize company information."""
        try:
            normalized_company = company.copy()
            
            # Clean company name
            if normalized_company.name:
                normalized_company.name = self._clean_company_name(normalized_company.name)
            
            # Normalize industry
            if normalized_company.industry:
                normalized_company.industry = self._normalize_industry(normalized_company.industry)
            
            return normalized_company
            
        except Exception as e:
            logger.warning(f"Failed to normalize company: {str(e)}")
            return company
    
    def _clean_company_name(self, name: str) -> str:
        """Clean company name."""
        if not name:
            return name
        
        # Remove common suffixes for normalization
        suffixes = [' Inc.', ' Inc', ' LLC', ' Ltd.', ' Ltd', ' Corp.', ' Corp', ' Co.', ' Co']
        
        cleaned_name = name
        for suffix in suffixes:
            if cleaned_name.endswith(suffix):
                cleaned_name = cleaned_name[:-len(suffix)]
                break
        
        return cleaned_name.strip()
    
    def _normalize_industry(self, industry: str) -> str:
        """Normalize industry names."""
        industry_mappings = {
            "computer software": "Software",
            "information technology and services": "Technology",
            "financial services": "Finance",
            "management consulting": "Consulting",
            "hospital & health care": "Healthcare"
        }
        
        industry_lower = industry.lower()
        return industry_mappings.get(industry_lower, industry)


class ExperienceProcessor:
    """Processor for LinkedIn experience data."""
    
    def process_experience(self, experience: LinkedInExperience) -> LinkedInExperience:
        """Process and normalize experience entry."""
        try:
            processed_exp = experience.copy()
            
            # Normalize company
            if processed_exp.company:
                company_normalizer = CompanyNormalizer()
                processed_exp.company = company_normalizer.normalize_company(processed_exp.company)
            
            # Process dates
            processed_exp = self._process_dates(processed_exp)
            
            # Calculate duration if missing
            if not processed_exp.duration and processed_exp.start_date:
                processed_exp.duration = self._calculate_duration(
                    processed_exp.start_date, processed_exp.end_date
                )
            
            return processed_exp
            
        except Exception as e:
            logger.warning(f"Failed to process experience: {str(e)}")
            return experience
    
    def _process_dates(self, experience: LinkedInExperience) -> LinkedInExperience:
        """Process and normalize date fields."""
        # This would involve parsing various date formats
        # For now, keep dates as-is
        return experience
    
    def _calculate_duration(self, start_date: str, end_date: Optional[str]) -> str:
        """Calculate duration between dates."""
        try:
            # Simplified duration calculation
            if not end_date:
                return "Present"
            
            # This would involve proper date parsing and calculation
            return "Duration calculated"
            
        except Exception:
            return "Unknown duration"


class EducationProcessor:
    """Processor for LinkedIn education data."""
    
    def process_education(self, education: LinkedInEducation) -> LinkedInEducation:
        """Process and normalize education entry."""
        try:
            processed_edu = education.copy()
            
            # Normalize school name
            if processed_edu.school:
                processed_edu.school = self._normalize_school_name(processed_edu.school)
            
            # Normalize degree
            if processed_edu.degree:
                processed_edu.degree = self._normalize_degree(processed_edu.degree)
            
            return processed_edu
            
        except Exception as e:
            logger.warning(f"Failed to process education: {str(e)}")
            return education
    
    def _normalize_school_name(self, school: str) -> str:
        """Normalize school names."""
        # Remove common suffixes
        suffixes = [' University', ' College', ' Institute', ' School']
        
        normalized = school
        for suffix in suffixes:
            if normalized.endswith(suffix):
                normalized = normalized[:-len(suffix)] + suffix.lower().title()
                break
        
        return normalized
    
    def _normalize_degree(self, degree: str) -> str:
        """Normalize degree names."""
        degree_mappings = {
            "bachelor of science": "Bachelor of Science",
            "bachelor of arts": "Bachelor of Arts",
            "master of science": "Master of Science",
            "master of business administration": "Master of Business Administration",
            "doctor of philosophy": "Doctor of Philosophy"
        }
        
        degree_lower = degree.lower()
        return degree_mappings.get(degree_lower, degree)


class SkillProcessor:
    """Processor for LinkedIn skills data."""
    
    def process_skills(self, skills: List[LinkedInSkill]) -> List[LinkedInSkill]:
        """Process and normalize skills list."""
        try:
            processed_skills = []
            seen_skills = set()
            
            for skill in skills:
                normalized_skill = self._normalize_skill(skill)
                
                # Avoid duplicates
                skill_key = normalized_skill.name.lower()
                if skill_key not in seen_skills:
                    processed_skills.append(normalized_skill)
                    seen_skills.add(skill_key)
            
            # Sort by endorsements (if available)
            processed_skills.sort(key=lambda s: s.endorsements or 0, reverse=True)
            
            return processed_skills
            
        except Exception as e:
            logger.warning(f"Failed to process skills: {str(e)}")
            return skills
    
    def _normalize_skill(self, skill: LinkedInSkill) -> LinkedInSkill:
        """Normalize individual skill."""
        try:
            normalized_skill = skill.copy()
            
            # Normalize skill name
            if normalized_skill.name:
                normalized_skill.name = self._normalize_skill_name(normalized_skill.name)
            
            return normalized_skill
            
        except Exception as e:
            logger.warning(f"Failed to normalize skill: {str(e)}")
            return skill
    
    def _normalize_skill_name(self, skill_name: str) -> str:
        """Normalize skill names."""
        # Common skill name mappings
        skill_mappings = {
            "javascript": "JavaScript",
            "python": "Python",
            "java": "Java",
            "c++": "C++",
            "c#": "C#",
            "html": "HTML",
            "css": "CSS",
            "sql": "SQL",
            "aws": "Amazon Web Services",
            "gcp": "Google Cloud Platform"
        }
        
        skill_lower = skill_name.lower()
        return skill_mappings.get(skill_lower, skill_name)


# Convenience functions
def process_linkedin_search_response(response: LinkedInSearchResponse) -> LinkedInSearchResponse:
    """Process LinkedIn search response."""
    processor = LinkedInResponseProcessor()
    return processor.process_search_response(response)


def process_linkedin_profile(profile: LinkedInProfile) -> LinkedInProfile:
    """Process individual LinkedIn profile."""
    processor = LinkedInResponseProcessor()
    return processor.process_profile(profile)
