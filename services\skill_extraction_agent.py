# services/skill_extraction_agent.py
"""
Simple Skill Extraction Agent for analyzing job profiles and extracting skills.
This agent analyzes job profiles and extracts required skills without complex RAG.
"""

import os
import logging
from typing import List, Dict, Any
import json

from langchain_openai import AzureChatOpenAI

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SkillExtractionAgent:
    """Simple agent for extracting skills from job profiles"""
    
    def __init__(self):
        # Initialize PDF extraction service
        from services.pdf_extraction_service import get_pdf_extraction_service
        self.pdf_service = get_pdf_extraction_service()
        
        # Initialize LLM for skill extraction
        self.llm = AzureChatOpenAI(
            model=os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME", "gpt-4o-mini"),
            temperature=0.0  # Consistent skill extraction
        )
    
    def generate_skills_for_job_profile(self, file_path: str) -> Dict[str, Any]:
        """
        Generate recommended skills based on job profile PDF analysis

        Args:
            file_path: Path to the job profile PDF

        Returns:
            Dictionary containing AI-generated skill recommendations and metadata
        """
        try:
            # Extract text from PDF
            extraction_result = self.pdf_service.extract_text_from_pdf(file_path)
            
            if not extraction_result.get('text'):
                raise ValueError("No text could be extracted from the PDF")
            
            # Generate skill recommendations using LLM
            skills = self._analyze_text_for_skills(extraction_result['text'])
            
            return {
                'skills': skills,
                'metadata': extraction_result.get('metadata', {}),
                'success': True
            }
            
        except Exception as e:
            logger.error(f"Error generating skills for job profile: {e}")
            return {
                'skills': [],
                'metadata': {},
                'success': False,
                'error': str(e)
            }
    
    def _analyze_text_for_skills(self, text: str) -> List[Dict[str, Any]]:
        """
        Generate recommended skills based on job profile analysis

        Args:
            text: Job profile text content to analyze

        Returns:
            List of AI-generated recommended skills with categories
        """
        prompt = f"""
        You are an HR expert. Based on the following job profile, generate a comprehensive list of skills that a candidate SHOULD have for this role.

        IMPORTANT: You MUST always recommend at least 5-10 relevant skills for any role, even if the job description is minimal. Use your expertise to infer what skills would be needed based on the role title and context.

        For each recommended skill, provide:
        1. skill_name: The name of the skill
        2. category: One of "technical", "soft", "language", "certification", "experience"
        3. importance: One of "required", "preferred", "nice_to_have"
        4. rationale: Why this skill is important for this role

        Return the result as a JSON array of objects.

        Job Profile:
        {text}

        Example format:
        [
            {{
                "skill_name": "Python Programming",
                "category": "technical",
                "importance": "required",
                "rationale": "Essential for backend development and data processing tasks"
            }},
            {{
                "skill_name": "Team Leadership",
                "category": "soft",
                "importance": "preferred",
                "rationale": "Important for senior roles to mentor junior developers"
            }}
        ]

        MANDATORY: Generate at least 5 comprehensive skill recommendations for this role, focusing on both technical and soft skills that would be essential for success:
        """
        
        try:
            logger.info(f"Calling LLM with prompt: {prompt[:300]}...")
            response = self.llm.invoke(prompt)

            # Parse the JSON response
            skills_text = response.content.strip()
            logger.info(f"LLM response: {skills_text[:500]}...")
            
            # Clean up the response (remove markdown formatting if present)
            if skills_text.startswith('```json'):
                skills_text = skills_text.replace('```json', '').replace('```', '').strip()
            elif skills_text.startswith('```'):
                skills_text = skills_text.replace('```', '').strip()
            
            skills = json.loads(skills_text)
            logger.info(f"Parsed JSON skills data: {skills}")

            # Validate and clean the skills
            validated_skills = []
            for skill in skills:
                if isinstance(skill, dict) and 'skill_name' in skill:
                    validated_skill = {
                        'skill_name': skill.get('skill_name', '').strip(),
                        'category': skill.get('category', 'technical').lower(),
                        'importance': skill.get('importance', 'required').lower(),
                        'rationale': skill.get('rationale', '').strip()
                    }
                    
                    # Only add if skill name is not empty
                    if validated_skill['skill_name']:
                        validated_skills.append(validated_skill)
            
            logger.info(f"Generated {len(validated_skills)} skill recommendations for job profile")
            return validated_skills
            
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing JSON response: {e}")
            return []
        except Exception as e:
            logger.error(f"Error generating skill recommendations: {e}")
            logger.error(f"Exception type: {type(e).__name__}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return []
    
    def combine_skills_with_position(self, profile_skills: List[Dict[str, Any]], position_skills: List[str]) -> List[str]:
        """
        Combine skills from job profile with job position skills
        
        Args:
            profile_skills: Skills extracted from job profile
            position_skills: Skills from job position
            
        Returns:
            Combined list of unique skills
        """
        all_skills = set()
        
        # Add profile skills
        for skill in profile_skills:
            skill_name = skill.get('skill_name', '').strip()
            if skill_name:
                all_skills.add(skill_name)
        
        # Add position skills
        for skill in position_skills:
            if isinstance(skill, str) and skill.strip():
                all_skills.add(skill.strip())
        
        # Convert to sorted list
        combined_skills = sorted(list(all_skills))
        
        logger.info(f"Combined {len(combined_skills)} unique skills")
        return combined_skills
    
    def format_skills_for_prompt(self, skills: List[str]) -> str:
        """
        Format skills list for use in interview generation prompt
        
        Args:
            skills: List of skills
            
        Returns:
            Formatted string for prompt
        """
        if not skills:
            return "No specific skills identified."
        
        # Group skills by type if possible (basic categorization)
        technical_keywords = ['programming', 'development', 'software', 'coding', 'database', 'framework', 'api', 'cloud', 'devops']
        soft_keywords = ['leadership', 'communication', 'teamwork', 'management', 'collaboration', 'problem-solving']
        
        technical_skills = []
        soft_skills = []
        other_skills = []
        
        for skill in skills:
            skill_lower = skill.lower()
            if any(keyword in skill_lower for keyword in technical_keywords):
                technical_skills.append(skill)
            elif any(keyword in skill_lower for keyword in soft_keywords):
                soft_skills.append(skill)
            else:
                other_skills.append(skill)
        
        formatted_parts = []
        
        if technical_skills:
            formatted_parts.append(f"Technical Skills: {', '.join(technical_skills)}")
        
        if soft_skills:
            formatted_parts.append(f"Soft Skills: {', '.join(soft_skills)}")
        
        if other_skills:
            formatted_parts.append(f"Other Skills: {', '.join(other_skills)}")
        
        return "\n".join(formatted_parts)


# Service factory function
def get_skill_extraction_agent() -> SkillExtractionAgent:
    """Get skill extraction agent instance"""
    return SkillExtractionAgent()
