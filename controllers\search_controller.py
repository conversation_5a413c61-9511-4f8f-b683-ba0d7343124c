"""
Search controller for professional data search across multiple providers.
Implements API endpoints for the Adapter Pattern search service.
"""

import logging
from typing import List, Optional, Dict, Any
from fastapi import HTTPException
import psycopg2

from services.search_service import search_service
from models.professionals import Professional, ProfessionalCreate

# Setup logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


async def search_professionals_across_providers(
    query: str,
    providers: Optional[List[str]] = None,
    location: Optional[str] = None,
    skills: Optional[List[str]] = None,
    experience_level: Optional[str] = None,
    limit: Optional[int] = 10,
    created_by: str = "api_user"
) -> Dict[str, Any]:
    """
    Search for professionals across multiple external providers.
    
    Args:
        query: Search query string
        providers: List of provider names to search (e.g., ["linkedin", "indeed"])
        location: Location filter (e.g., "San Francisco, CA")
        skills: Skills filter (e.g., ["Python", "React"])
        experience_level: Experience level filter ("junior", "mid", "senior")
        limit: Maximum results per provider (default: 10)
        created_by: User who initiated the search
        
    Returns:
        Dict containing unified search results from all providers
        
    Raises:
        HTTPException: If search fails or invalid parameters provided
    """
    try:
        logger.info(f"Starting professional search - Query: {query}, Providers: {providers}")
        
        # Validate query
        if not query or len(query.strip()) < 2:
            raise HTTPException(
                status_code=400, 
                detail="Query must be at least 2 characters long"
            )
        
        # Validate limit
        if limit and (limit < 1 or limit > 50):
            raise HTTPException(
                status_code=400, 
                detail="Limit must be between 1 and 50"
            )
        
        # Validate experience level
        valid_experience_levels = ["junior", "mid", "senior", None]
        if experience_level and experience_level.lower() not in valid_experience_levels:
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid experience level. Must be one of: {valid_experience_levels[:-1]}"
            )
        
        # Call search service
        results = await search_service.search_professionals(
            query=query.strip(),
            providers=providers,
            location=location,
            skills=skills,
            experience_level=experience_level.lower() if experience_level else None,
            limit=limit,
            created_by=created_by
        )
        
        logger.info(f"Search completed successfully. Total results: {results.get('total_results', 0)}")
        return results
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in search_professionals_across_providers: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")


async def get_available_providers() -> Dict[str, Any]:
    """
    Get list of available search providers.
    
    Returns:
        Dict containing available providers and their information
    """
    try:
        logger.info("Getting available providers")
        
        providers = search_service.get_available_providers()
        provider_info = {}
        
        for provider_name in providers:
            try:
                info = search_service.get_provider_info(provider_name)
                provider_info[provider_name] = info
            except Exception as e:
                logger.error(f"Error getting info for provider {provider_name}: {str(e)}")
                provider_info[provider_name] = {
                    "name": provider_name,
                    "available": False,
                    "error": str(e)
                }
        
        return {
            "available_providers": providers,
            "provider_details": provider_info,
            "total_providers": len(providers)
        }
        
    except Exception as e:
        logger.error(f"Error getting available providers: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get providers: {str(e)}")


async def search_and_save_professionals(
    query: str,
    providers: Optional[List[str]] = None,
    location: Optional[str] = None,
    skills: Optional[List[str]] = None,
    experience_level: Optional[str] = None,
    limit: Optional[int] = 10,
    created_by: str = "api_user",
    save_to_database: bool = False
) -> Dict[str, Any]:
    """
    Search for professionals and optionally save them to the database.
    
    Args:
        query: Search query string
        providers: List of provider names to search
        location: Location filter
        skills: Skills filter
        experience_level: Experience level filter
        limit: Maximum results per provider
        created_by: User who initiated the search
        save_to_database: Whether to save results to database
        
    Returns:
        Dict containing search results and save status
    """
    try:
        logger.info(f"Search and save request - Query: {query}, Save: {save_to_database}")
        
        # Perform search
        search_results = await search_professionals_across_providers(
            query=query,
            providers=providers,
            location=location,
            skills=skills,
            experience_level=experience_level,
            limit=limit,
            created_by=created_by
        )
        
        response = {
            "search_results": search_results,
            "saved_to_database": False,
            "saved_count": 0,
            "save_errors": []
        }
        
        # Save to database if requested
        if save_to_database:
            try:
                saved_count, save_errors = await _save_professionals_to_database(
                    search_results.get("professionals", []),
                    created_by
                )
                
                response.update({
                    "saved_to_database": True,
                    "saved_count": saved_count,
                    "save_errors": save_errors
                })
                
                logger.info(f"Saved {saved_count} professionals to database")
                
            except Exception as e:
                logger.error(f"Error saving to database: {str(e)}")
                response["save_errors"].append(f"Database save failed: {str(e)}")
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in search_and_save_professionals: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Search and save failed: {str(e)}")


async def _save_professionals_to_database(
    professionals: List[Dict[str, Any]], 
    created_by: str
) -> tuple[int, List[str]]:
    """
    Save professionals to the database.
    
    Args:
        professionals: List of professional data
        created_by: User who initiated the save
        
    Returns:
        Tuple of (saved_count, error_list)
    """
    saved_count = 0
    errors = []
    
    # Note: This is a placeholder implementation
    # In a real implementation, you would:
    # 1. Connect to the database
    # 2. Check for duplicates
    # 3. Insert new professionals
    # 4. Generate embeddings
    # 5. Handle errors gracefully
    
    try:
        for professional_data in professionals:
            try:
                # Create ProfessionalCreate model
                professional_create = ProfessionalCreate(
                    professional_info=professional_data.get("professional_info"),
                    to_be_embebbed=professional_data.get("to_be_embebbed"),
                    source=professional_data.get("source"),
                    created_by=created_by
                )
                
                # TODO: Implement actual database save
                # For now, just count as saved
                saved_count += 1
                
                logger.debug(f"Would save professional from {professional_data.get('source')}")
                
            except Exception as e:
                error_msg = f"Failed to save professional: {str(e)}"
                errors.append(error_msg)
                logger.error(error_msg)
        
    except Exception as e:
        error_msg = f"Database operation failed: {str(e)}"
        errors.append(error_msg)
        logger.error(error_msg)
    
    return saved_count, errors


async def get_search_statistics() -> Dict[str, Any]:
    """
    Get search service statistics and health information.
    
    Returns:
        Dict containing service statistics
    """
    try:
        logger.info("Getting search statistics")
        
        providers = search_service.get_available_providers()
        provider_status = {}
        
        for provider_name in providers:
            try:
                info = search_service.get_provider_info(provider_name)
                provider_status[provider_name] = "healthy"
            except Exception as e:
                provider_status[provider_name] = f"error: {str(e)}"
        
        return {
            "service_status": "healthy",
            "total_providers": len(providers),
            "available_providers": providers,
            "provider_status": provider_status,
            "supported_features": {
                "multi_provider_search": True,
                "tag_generation": True,
                "source_attribution": True,
                "database_save": True,
                "filtering": {
                    "location": True,
                    "skills": True,
                    "experience_level": True
                }
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting search statistics: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get statistics: {str(e)}")
