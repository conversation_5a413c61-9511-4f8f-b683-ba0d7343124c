#!/usr/bin/env python3
"""
Ideal Candidate Service for Position-Based Candidate Generation and Matching.

This service generates ideal candidate profiles from position descriptions and 
processes them through the chunked embedding system for enhanced matching 
capabilities. It provides a complete pipeline from position analysis to 
vectorized ideal candidate profiles.

Key Features:
- Generates comprehensive ideal candidates from position descriptions
- Processes ideal candidates through chunked embedding system
- Provides position-candidate similarity scoring
- Supports gap analysis between real and ideal candidates
- Enables recruitment guidance and candidate evaluation
"""

import logging
import asyncio
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime

from utils.ideal_candidate_generator import IdealCandidateGenerator
from services.chunked_embedding_service import ChunkedEmbeddingService
from utils.lumus_markdown_processor import LumusMarkdownProcessor

logger = logging.getLogger(__name__)


class IdealCandidateService:
    """
    Service for generating and processing ideal candidates for position matching.
    
    This service combines ideal candidate generation with chunked embedding 
    processing to create comprehensive reference profiles for position matching.
    """
    
    def __init__(self):
        """Initialize the ideal candidate service."""
        self.candidate_generator = IdealCandidateGenerator()
        self.chunked_embedding_service = ChunkedEmbeddingService()
        self.markdown_processor = LumusMarkdownProcessor(
            chunk_size=25000,  # Large chunks - only split if content exceeds 90% of embedding model limit
            chunk_overlap=500,
            max_chunks=2  # Maximum 2 chunks to keep it simple
        )
    
    async def generate_ideal_candidate_with_embeddings(self, position_description: str) -> Dict[str, Any]:
        """
        Generate an ideal candidate profile with chunked embeddings from position description.
        
        Args:
            position_description (str): Complete position description text
            
        Returns:
            Dict[str, Any]: Complete ideal candidate with embeddings and analysis
        """
        try:
            logger.info("Starting ideal candidate generation and embedding process")
            
            # Step 1: Generate ideal candidate profile
            ideal_candidate = self.candidate_generator.generate_ideal_candidate(position_description)
            
            # Step 2: Create mock Lumus response structure
            mock_lumus_response = self._create_mock_lumus_response(ideal_candidate, position_description)
            
            # Step 3: Process through chunked embedding system
            embedding_result = await self.chunked_embedding_service.process_lumus_response_with_chunks(mock_lumus_response)
            
            if not embedding_result['success']:
                return {
                    'success': False,
                    'error': f"Embedding processing failed: {embedding_result.get('error')}",
                    'ideal_candidate': ideal_candidate
                }
            
            # Step 4: Compile comprehensive result
            result = {
                'success': True,
                'position_analysis': self._analyze_position_requirements(position_description),
                'ideal_candidate': ideal_candidate,
                'embedding_data': {
                    'full_markdown': embedding_result['full_markdown'],
                    'chunks': embedding_result['chunks'],
                    'chunk_count': embedding_result['chunk_count'],
                    'aggregated_embedding': embedding_result['aggregated_embedding'],
                    'total_characters': embedding_result['total_characters']
                },
                'generation_metadata': {
                    'generated_at': datetime.now().isoformat(),
                    'position_hash': hash(position_description),
                    'processing_stats': self.chunked_embedding_service.get_processing_stats(embedding_result)
                }
            }
            
            logger.info(f"Successfully generated ideal candidate with {embedding_result['chunk_count']} embedding chunks")
            return result
            
        except Exception as e:
            logger.error(f"Error in ideal candidate generation: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'generated_at': datetime.now().isoformat()
            }
    
    def _create_mock_lumus_response(self, ideal_candidate: Dict[str, Any], position_description: str) -> Dict[str, Any]:
        """Create a mock Lumus response structure for the ideal candidate."""
        return {
            'task_id': f'ideal-candidate-{hash(position_description)}',
            'action': 'ideal_candidate_generation',
            'status': 'completed',
            'created_at': datetime.now().timestamp(),
            'file_name': 'Ideal_Candidate_Profile.json',
            'result': {
                'response': ideal_candidate,
                'extraction_cost': {
                    'prompt_tokens': 0,
                    'completion_tokens': 0,
                    'total_tokens': 0,
                    'cost': 0.0
                }
            },
            'processing_time': 0.0,
            'completed_at': datetime.now().timestamp()
        }
    
    def _analyze_position_requirements(self, position_description: str) -> Dict[str, Any]:
        """Analyze position requirements for metadata."""
        # Extract key information for analysis
        analysis = {
            'position_complexity': self._assess_position_complexity(position_description),
            'skill_requirements': self._count_skill_requirements(position_description),
            'seniority_indicators': self._identify_seniority_indicators(position_description),
            'domain_focus': self._identify_domain_focus(position_description),
            'remote_friendly': 'remote' in position_description.lower(),
            'description_length': len(position_description),
            'sections_identified': self._count_sections(position_description)
        }
        
        return analysis
    
    def _assess_position_complexity(self, text: str) -> str:
        """Assess the complexity level of the position."""
        complexity_indicators = {
            'high': ['lead', 'principal', 'architect', 'senior', 'expert', 'advanced'],
            'medium': ['intermediate', 'mid', 'experienced', 'proficient'],
            'low': ['junior', 'entry', 'beginner', 'basic', 'associate']
        }
        
        text_lower = text.lower()
        
        for level, indicators in complexity_indicators.items():
            if any(indicator in text_lower for indicator in indicators):
                return level
        
        return 'medium'  # Default
    
    def _count_skill_requirements(self, text: str) -> Dict[str, int]:
        """Count different types of skill requirements."""
        import re
        
        # Count professional skills
        prof_skills_match = re.search(r'\*\*Professional Skills:\*\*\s*\n(.*?)(?=\n\s*\*\*|\n\s*-\s*\*\*|$)', text, re.DOTALL)
        prof_skills_count = len(re.findall(r'-\s*([^(]+)\s*\(([^)]+)\)', prof_skills_match.group(1))) if prof_skills_match else 0
        
        # Count nice-to-have skills
        nice_skills_match = re.search(r'\*\*Nice to Have:\*\*\s*\n(.*?)(?=\n\s*\*\*|\n\s*-\s*\*\*|$)', text, re.DOTALL)
        nice_skills_count = len(re.findall(r'-\s*([^(]+)\s*\(([^)]+)\)', nice_skills_match.group(1))) if nice_skills_match else 0
        
        # Count soft skills
        soft_skills_match = re.search(r'\*\*Soft Skills:\*\*\s*\n(.*?)(?=\n\s*\*\*|\n\s*-\s*\*\*|$)', text, re.DOTALL)
        soft_skills_count = len(re.findall(r'-\s*([^(]+)\s*\(([^)]+)\)', soft_skills_match.group(1))) if soft_skills_match else 0
        
        return {
            'required_skills': prof_skills_count,
            'nice_to_have_skills': nice_skills_count,
            'soft_skills': soft_skills_count,
            'total_skills': prof_skills_count + nice_skills_count + soft_skills_count
        }
    
    def _identify_seniority_indicators(self, text: str) -> List[str]:
        """Identify seniority level indicators in the text."""
        seniority_terms = ['junior', 'senior', 'lead', 'principal', 'staff', 'entry', 'mid', 'intermediate', 'advanced', 'expert']
        text_lower = text.lower()
        
        found_indicators = []
        for term in seniority_terms:
            if term in text_lower:
                found_indicators.append(term)
        
        return found_indicators
    
    def _identify_domain_focus(self, text: str) -> List[str]:
        """Identify the domain focus areas of the position."""
        domain_keywords = {
            'ai_ml': ['ai', 'artificial intelligence', 'machine learning', 'deep learning', 'neural network', 'llm', 'gpt'],
            'web_development': ['web', 'frontend', 'backend', 'full stack', 'react', 'angular', 'vue'],
            'mobile': ['mobile', 'ios', 'android', 'react native', 'flutter'],
            'cloud': ['cloud', 'aws', 'azure', 'gcp', 'kubernetes', 'docker'],
            'data': ['data', 'analytics', 'big data', 'etl', 'data science', 'sql'],
            'devops': ['devops', 'ci/cd', 'infrastructure', 'deployment', 'monitoring'],
            'security': ['security', 'cybersecurity', 'encryption', 'authentication']
        }
        
        text_lower = text.lower()
        identified_domains = []
        
        for domain, keywords in domain_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                identified_domains.append(domain)
        
        return identified_domains
    
    def _count_sections(self, text: str) -> int:
        """Count the number of structured sections in the position description."""
        import re
        sections = re.findall(r'\*\*[^*]+\*\*', text)
        return len(sections)
    
    def extract_position_required_skills(self, position_description: str) -> List[str]:
        """Extract technical skills from position description."""
        tech_skills_keywords = [
            'JavaScript', 'TypeScript', 'React', 'Angular', 'Vue',
            'Node.js', 'Python', 'Java', 'C#', 'PHP', 'Ruby',
            'AWS', 'Azure', 'GCP', 'Docker', 'Kubernetes',
            'PostgreSQL', 'MySQL', 'MongoDB', 'Redis',
            'Git', 'CI/CD', 'REST', 'GraphQL', 'API', 'HTML', 'CSS'
        ]

        found_skills = []
        position_lower = position_description.lower()

        for skill in tech_skills_keywords:
            if skill.lower() in position_lower:
                found_skills.append(skill)

        return found_skills

    def calculate_skill_weight_penalty(self, candidate_skills: List[Dict], required_skills: List[str]) -> Dict[str, Any]:
        """Calculate penalty based on absence of required technical skills."""

        # Define comprehensive technical skills list
        all_tech_skills = [
            'JavaScript', 'TypeScript', 'React', 'Angular', 'Vue',
            'Node.js', 'Python', 'Java', 'C#', 'PHP', 'Ruby',
            'AWS', 'Azure', 'GCP', 'Docker', 'Kubernetes',
            'PostgreSQL', 'MySQL', 'MongoDB', 'Redis',
            'Git', 'CI/CD', 'REST', 'GraphQL', 'API', 'HTML', 'CSS'
        ]

        # Extract candidate's technical skills
        candidate_tech_skills = [
            skill['name'] for skill in candidate_skills
            if skill['name'] in all_tech_skills
        ]

        # Calculate coverage of required skills
        required_skills_found = [
            skill for skill in candidate_tech_skills
            if skill in required_skills
        ]

        # Calculate overall tech skill presence
        tech_skill_count = len(candidate_tech_skills)
        required_skill_coverage = len(required_skills_found) / len(required_skills) if required_skills else 0

        # Determine weight based on technical skills presence
        if tech_skill_count == 0:
            # No technical skills at all - severe penalty
            weight = 0.2
            penalty_reason = "No technical skills found"
        elif required_skill_coverage >= 0.7:
            # 70%+ of required skills - no penalty
            weight = 1.0
            penalty_reason = "Excellent skill match"
        elif required_skill_coverage >= 0.5:
            # 50-70% of required skills - light penalty
            weight = 0.85
            penalty_reason = "Good skill match"
        elif required_skill_coverage >= 0.3:
            # 30-50% of required skills - moderate penalty
            weight = 0.65
            penalty_reason = "Partial skill match"
        elif tech_skill_count >= 3:
            # Has some tech skills but not required ones - moderate penalty
            weight = 0.5
            penalty_reason = "Different technical skills"
        else:
            # Few or no relevant tech skills - heavy penalty
            weight = 0.3
            penalty_reason = "Limited technical skills"

        return {
            'weight': weight,
            'penalty_percentage': (1 - weight) * 100,
            'candidate_tech_skills': candidate_tech_skills,
            'required_skills_found': required_skills_found,
            'required_skill_coverage': required_skill_coverage,
            'penalty_reason': penalty_reason
        }

    async def compare_candidate_to_ideal(self,
                                       real_candidate_chunks: List[Dict[str, Any]],
                                       ideal_candidate_result: Dict[str, Any],
                                       position_description: str = None,
                                       apply_skill_weighting: bool = True) -> Dict[str, Any]:
        """
        Compare a real candidate's chunks to the ideal candidate.
        
        Args:
            real_candidate_chunks (List[Dict[str, Any]]): Real candidate's embedding chunks
            ideal_candidate_result (Dict[str, Any]): Ideal candidate result from generate_ideal_candidate_with_embeddings
            
        Returns:
            Dict[str, Any]: Detailed comparison analysis
        """
        try:
            if not ideal_candidate_result['success']:
                return {'success': False, 'error': 'Invalid ideal candidate data'}
            
            ideal_chunks = ideal_candidate_result['embedding_data']['chunks']
            
            # Calculate chunk-level similarities
            chunk_similarities = []
            
            for real_chunk in real_candidate_chunks:
                if not real_chunk.get('embedding_success', False):
                    continue
                
                best_match = None
                best_similarity = 0.0
                
                for ideal_chunk in ideal_chunks:
                    if not ideal_chunk.get('embedding_success', False):
                        continue
                    
                    similarity = self.chunked_embedding_service._cosine_similarity(
                        real_chunk['dense_embedding'],
                        ideal_chunk['dense_embedding']
                    )
                    
                    if similarity > best_similarity:
                        best_similarity = similarity
                        best_match = ideal_chunk
                
                if best_match:
                    chunk_similarities.append({
                        'real_chunk': {
                            'index': real_chunk['index'],
                            'content_type': real_chunk['content_type'],
                            'section': real_chunk['section']
                        },
                        'ideal_match': {
                            'index': best_match['index'],
                            'content_type': best_match['content_type'],
                            'section': best_match['section']
                        },
                        'similarity_score': best_similarity
                    })
            
            # Calculate overall similarity using aggregated embeddings
            overall_similarity = 0.0
            if (real_candidate_chunks and 
                ideal_candidate_result['embedding_data']['aggregated_embedding']['success']):
                
                # Create aggregated embedding for real candidate
                real_valid_chunks = [chunk for chunk in real_candidate_chunks if chunk.get('embedding_success', False)]
                if real_valid_chunks:
                    real_aggregated = self.chunked_embedding_service._create_aggregated_embedding(real_valid_chunks)
                    
                    if real_aggregated['success']:
                        overall_similarity = self.chunked_embedding_service._cosine_similarity(
                            real_aggregated['dense_embedding'],
                            ideal_candidate_result['embedding_data']['aggregated_embedding']['dense_embedding']
                        )
            
            # Generate gap analysis
            gap_analysis = self._generate_gap_analysis(chunk_similarities, ideal_candidate_result)
            
            # Apply skill weighting if requested and position description is provided
            skill_weighted_similarity = overall_similarity
            skill_analysis = None

            if apply_skill_weighting and position_description:
                # Extract required skills from position
                required_skills = self.extract_position_required_skills(position_description)

                # Get candidate data from first chunk
                candidate_data = real_candidate_chunks[0].get('original_data', {}) if real_candidate_chunks else {}

                # Calculate skill weight penalty
                skill_analysis = self.calculate_skill_weight_penalty(
                    candidate_data.get('skills', []),
                    required_skills
                )

                # Apply skill weighting
                skill_weighted_similarity = overall_similarity * skill_analysis['weight']

                logger.info(f"Applied skill weighting: {overall_similarity:.3f} → {skill_weighted_similarity:.3f} (penalty: {skill_analysis['penalty_percentage']:.1f}%)")

            return {
                'success': True,
                'overall_similarity': overall_similarity,
                'skill_weighted_similarity': skill_weighted_similarity,
                'skill_weighting_applied': apply_skill_weighting and position_description is not None,
                'skill_analysis': skill_analysis,
                'chunk_similarities': chunk_similarities,
                'average_chunk_similarity': sum(cs['similarity_score'] for cs in chunk_similarities) / len(chunk_similarities) if chunk_similarities else 0.0,
                'gap_analysis': gap_analysis,
                'comparison_metadata': {
                    'real_chunks_count': len(real_candidate_chunks),
                    'ideal_chunks_count': len(ideal_chunks),
                    'successful_comparisons': len(chunk_similarities),
                    'compared_at': datetime.now().isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"Error comparing candidate to ideal: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'compared_at': datetime.now().isoformat()
            }
    
    def _generate_gap_analysis(self, chunk_similarities: List[Dict[str, Any]], ideal_candidate_result: Dict[str, Any]) -> Dict[str, Any]:
        """Generate gap analysis between real and ideal candidate."""
        ideal_chunks = ideal_candidate_result['embedding_data']['chunks']
        
        # Identify missing content types
        real_content_types = set(cs['real_chunk']['content_type'] for cs in chunk_similarities)
        ideal_content_types = set(chunk['content_type'] for chunk in ideal_chunks)
        missing_content_types = ideal_content_types - real_content_types
        
        # Identify weak areas (low similarity scores)
        weak_areas = [
            cs for cs in chunk_similarities 
            if cs['similarity_score'] < 0.7  # Threshold for weak similarity
        ]
        
        # Identify strong areas (high similarity scores)
        strong_areas = [
            cs for cs in chunk_similarities 
            if cs['similarity_score'] >= 0.8  # Threshold for strong similarity
        ]
        
        return {
            'missing_content_types': list(missing_content_types),
            'weak_areas': weak_areas,
            'strong_areas': strong_areas,
            'improvement_suggestions': self._generate_improvement_suggestions(missing_content_types, weak_areas)
        }
    
    def _generate_improvement_suggestions(self, missing_content_types: List[str], weak_areas: List[Dict[str, Any]]) -> List[str]:
        """Generate improvement suggestions based on gap analysis."""
        suggestions = []
        
        if 'skills' in missing_content_types:
            suggestions.append("Consider highlighting technical skills and proficiency levels more prominently")
        
        if 'projects' in missing_content_types:
            suggestions.append("Add relevant project experience to demonstrate practical application of skills")
        
        if 'certifications' in missing_content_types:
            suggestions.append("Obtain relevant certifications to strengthen technical credibility")
        
        for weak_area in weak_areas:
            content_type = weak_area['real_chunk']['content_type']
            if content_type == 'work_experience':
                suggestions.append("Enhance work experience descriptions with more specific achievements and technologies used")
            elif content_type == 'education':
                suggestions.append("Consider additional education or training in relevant areas")
        
        return suggestions
