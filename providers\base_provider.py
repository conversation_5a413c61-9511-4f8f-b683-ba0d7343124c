"""
Base provider interface for external professional data sources.
Implements the Adapter Pattern for standardizing provider responses.
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field


class SearchRequest(BaseModel):
    """Standardized search request model for all providers"""
    query: str = Field(..., description="Search query string")
    location: Optional[str] = Field(None, description="Location filter")
    skills: Optional[List[str]] = Field(None, description="Skills filter")
    experience_level: Optional[str] = Field(None, description="Experience level filter (junior, mid, senior)")
    limit: Optional[int] = Field(10, description="Maximum number of results to return")
    
    class Config:
        from_attributes = True


class ProviderResponse(BaseModel):
    """Standardized response model from providers"""
    professionals: List[Dict[str, Any]] = Field(..., description="List of professional data")
    total_found: int = Field(..., description="Total number of professionals found")
    provider_name: str = Field(..., description="Name of the provider")
    search_metadata: Optional[Dict[str, Any]] = Field(None, description="Additional search metadata")
    
    class Config:
        from_attributes = True


class BaseProvider(ABC):
    """
    Abstract base class for all professional data providers.
    Implements the Adapter Pattern to standardize different provider APIs.
    """
    
    def __init__(self, provider_name: str):
        self.provider_name = provider_name
    
    @abstractmethod
    async def search_professionals(self, request: SearchRequest) -> ProviderResponse:
        """
        Search for professionals using provider-specific API.
        Must be implemented by each provider.
        
        Args:
            request: Standardized search request
            
        Returns:
            ProviderResponse: Standardized response with professional data
        """
        pass
    
    @abstractmethod
    def adapt_to_professional_model(self, provider_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Adapt provider-specific data format to our Professional model structure.
        This is the core of the Adapter Pattern implementation.
        
        Args:
            provider_data: Raw data from the provider
            
        Returns:
            Dict: Data formatted to match our Professional model
        """
        pass
    
    def generate_tags(self, professional_data: Dict[str, Any]) -> List[str]:
        """
        Generate searchable tags from professional data.
        Can be overridden by specific providers for custom tag generation.
        
        Args:
            professional_data: Professional data in our standard format
            
        Returns:
            List[str]: Generated tags for search filtering
        """
        tags = []
        
        # Extract tags from roles
        if "roles" in professional_data:
            for role in professional_data["roles"]:
                # Convert role to lowercase and split into words
                role_words = role.lower().replace("-", " ").split()
                tags.extend(role_words)
        
        # Extract tags from skills
        if "skills" in professional_data:
            for skill in professional_data["skills"]:
                if isinstance(skill, dict) and "name" in skill:
                    skill_words = skill["name"].lower().replace("-", " ").split()
                    tags.extend(skill_words)
        
        # Extract tags from work experience
        if "work_experience" in professional_data:
            for exp in professional_data["work_experience"]:
                if isinstance(exp, dict) and "job_title" in exp:
                    title_words = exp["job_title"].lower().replace("-", " ").split()
                    tags.extend(title_words)
        
        # Remove duplicates and common words
        common_words = {"and", "or", "the", "a", "an", "in", "on", "at", "to", "for", "of", "with"}
        unique_tags = list(set([tag for tag in tags if tag not in common_words and len(tag) > 2]))
        
        return unique_tags[:20]  # Limit to 20 most relevant tags
    
    def get_provider_name(self) -> str:
        """Get the provider name"""
        return self.provider_name
