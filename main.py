import os
import logging

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from dotenv import load_dotenv
load_dotenv()
from routes.routes_direct import router as api_router_direct
from routes.routes_positions_profile import router as api_router_positions_profile
from routes.routes_recruiter import router as api_router_recruiter
from routes.routes_linkedin import router as api_router_external_source
from routes.routes_websocket import router as api_router_websocket
from routes.routes_note import router as api_router_note
from routes.routes_project import router as api_router_project
from routes.routes_position import router as api_router_position
from routes.routes_candidate import router as api_router_candidate
from routes.routes_interview import router as api_router_interview
from routes.routes_match import router as api_router_match
from routes.routes_professionals import router as api_router_professionals
from routes.routes_user import router as api_router_user
from routes.routes_roles import router as api_router_roles
from routes.routes_permissions import router as api_router_permissions
from routes.routes_auth import router as api_router_auth

# Importar configuración de Azure Monitor para OpenTelemetry
# from azure.monitor.opentelemetry import configure_azure_monitor

from opentelemetry import trace
from opentelemetry._logs import set_logger_provider
from opentelemetry.sdk._logs import (
    LoggerProvider,
    LoggingHandler,
)
from opentelemetry.sdk._logs.export import BatchLogRecordProcessor
from opentelemetry.sdk.trace import TracerProvider

from azure.monitor.opentelemetry.exporter import AzureMonitorLogExporter
trace.set_tracer_provider(TracerProvider())
tracer = trace.get_tracer(__name__)
logger_provider = LoggerProvider()
set_logger_provider(logger_provider)

# Configure Application Insights only if connection string is provided
app_insights_connection_string = os.getenv(
    "APPLICATIONINSIGHTS_CONNECTION_STRING")
if app_insights_connection_string:
    exporter = AzureMonitorLogExporter(
        connection_string=app_insights_connection_string
    )
    logger_provider.add_log_record_processor(BatchLogRecordProcessor(exporter))
    print("✅ Application Insights monitoring enabled")
else:
    print("ℹ️  Application Insights monitoring disabled (connection string not provided)")

# Configurar el logger de Python (los logs se enviarán a App Insights por OpenTelemetry si está configurado)
handler = LoggingHandler()
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.addHandler(handler)
logger.setLevel(logging.DEBUG)

app = FastAPI(title="Semantic Matching Service")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["Content-Disposition"],
)


# Importar tu router después de configurar la instrumentación

app.include_router(api_router_match, tags=["Match"])
app.include_router(api_router_candidate,
                   prefix="/candidate", tags=["Candidate"])
app.include_router(api_router_interview,
                   prefix="/interview", tags=["Interview"])
app.include_router(api_router_position, prefix="/position", tags=["Position"])
app.include_router(api_router_project, prefix="/project", tags=["Project"])
app.include_router(api_router_note, prefix="/note", tags=["Note"])
app.include_router(api_router_websocket, prefix="/ws", tags=["Websocket"])
app.include_router(api_router_external_source, prefix="/api",
                   tags=["LinkedIn Integration"])
app.include_router(api_router_recruiter,
                   prefix="/recruiter", tags=["Recruiter"])
# app.include_router(api_router_positions_profile,
#                    prefix="/positions_profile", tags=["Positions Profile"])
app.include_router(api_router_professionals,
                   prefix="/professionals", tags=["Professionals"])
app.include_router(api_router_user,
                   prefix="/user", tags=["User Management"])
app.include_router(api_router_roles,
                   prefix="/roles", tags=["Role Management"])
app.include_router(api_router_permissions,
                   prefix="/permissions", tags=["Permission Management"])
app.include_router(api_router_auth,
                   prefix="/auth", tags=["Authentication"])
app.include_router(api_router_direct, tags=["Direct API"])

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8080)
