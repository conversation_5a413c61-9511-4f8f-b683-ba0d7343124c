#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to update PyMuPDF to the compatible version for pdf2docx.
This script helps resolve the 'get_area' compatibility issue.
"""

import subprocess
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def run_command(command):
    """Run a shell command and return the result."""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, check=True)
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        logger.error(f"Command failed: {command}")
        logger.error(f"Error: {e.stderr}")
        return None

def check_current_version():
    """Check the currently installed PyMuPDF version."""
    try:
        import fitz
        current_version = fitz.version[0] if isinstance(fitz.version, tuple) else fitz.version
        logger.info(f"Current PyMuPDF version: {current_version}")
        return current_version
    except ImportError:
        logger.warning("PyMuPDF is not currently installed")
        return None

def update_pymupdf():
    """Update PyMuPDF to the compatible version."""
    logger.info("Updating PyMuPDF to version 1.26.4 for pdf2docx compatibility...")
    
    # First, uninstall current version
    logger.info("Uninstalling current PyMuPDF version...")
    uninstall_result = run_command("pip uninstall PyMuPDF -y")
    
    if uninstall_result is not None:
        logger.info("PyMuPDF uninstalled successfully")
    
    # Install specific version
    logger.info("Installing PyMuPDF==1.26.4...")
    install_result = run_command("pip install PyMuPDF==1.26.4")
    
    if install_result is not None:
        logger.info("PyMuPDF 1.26.4 installed successfully")
        return True
    else:
        logger.error("Failed to install PyMuPDF 1.26.4")
        return False

def verify_installation():
    """Verify that the correct version is installed and pdf2docx works."""
    try:
        import fitz
        from pdf2docx import Converter
        
        version = fitz.version[0] if isinstance(fitz.version, tuple) else fitz.version
        logger.info(f"Verified PyMuPDF version: {version}")
        
        if version == "1.26.4":
            logger.info("✅ Correct PyMuPDF version installed")
            logger.info("✅ pdf2docx import successful")
            return True
        else:
            logger.warning(f"⚠️  Expected version 1.26.4, but got {version}")
            return False
            
    except ImportError as e:
        logger.error(f"❌ Import failed: {e}")
        return False

def main():
    """Main function to update PyMuPDF and verify installation."""
    logger.info("🔧 PyMuPDF Compatibility Update Script")
    logger.info("=" * 50)
    
    # Check current version
    current_version = check_current_version()
    
    if current_version == "1.26.4":
        logger.info("✅ PyMuPDF 1.26.4 is already installed")
        if verify_installation():
            logger.info("🎉 No update needed - everything is compatible!")
            return 0
    
    # Update PyMuPDF
    if update_pymupdf():
        # Verify installation
        if verify_installation():
            logger.info("🎉 PyMuPDF successfully updated to compatible version!")
            logger.info("📝 The 'get_area' error should now be resolved.")
            return 0
        else:
            logger.error("💥 Installation verification failed")
            return 1
    else:
        logger.error("💥 Failed to update PyMuPDF")
        return 1

if __name__ == "__main__":
    sys.exit(main())
