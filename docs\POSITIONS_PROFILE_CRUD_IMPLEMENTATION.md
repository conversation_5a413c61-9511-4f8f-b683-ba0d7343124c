# Positions Profile CRUD Implementation

## Overview
Complete CRUD (Create, Read, Update, Delete) service implementation for the `positions_profile` functionality in the SmartHR backend application.

## Database Schema

The `positions_profile` table structure (from `scripts/init_db.sql`):

```sql
CREATE TABLE IF NOT EXISTS positions_profile (
    id                  UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    profile_info        JSONB,
    reason              TEXT,
    is_active           BOOLEAN DEFAULT true,
    is_deleted          BOOLEAN DEFAULT false,
    created_at          TIMESTAMP DEFAULT NOW(),
    created_by          TEXT,
    updated_at          TIMESTAMP DEFAULT NOW(),
    updated_by          TEXT
);
```

## Implementation Architecture

### 1. Service Layer (`services/positions_profile_service.py`)

**Purpose**: Handles business logic and database interactions

**Functions Implemented**:

- `get_cursor()` - Context manager for database connections with error handling
- `create_profile_position(profile_data)` - Create new profile position
- `get_profile_position_by_id(profile_id)` - Retrieve profile by ID
- `update_profile_position(profile_data)` - Update existing profile
- `delete_profile_position(profile_id, deleted_by)` - Soft delete profile
- `get_all_profile_positions(page, page_size, filters)` - Paginated list with filtering

**Key Features**:
- Uses `psycopg2` with `RealDictCursor` for dict-based results
- Implements soft delete (sets `is_deleted = true`)
- Comprehensive error handling with HTTPException
- Logging for debugging and monitoring
- Connection pooling with timeout settings

### 2. Controller Layer (`controllers/positions_profile_controller.py`)

**Purpose**: Handles request/response formatting and calls service layer

**Functions Implemented**:

- `create_profile_position(profile_data)` - Controller for creation
- `get_profile_position_by_id(profile_id)` - Controller for retrieval
- `update_profile_position(profile_data)` - Controller for updates
- `delete_profile_position(profile_id, deleted_by)` - Controller for deletion
- `get_all_profile_positions(page, page_size, filters)` - Controller for listing

**Key Features**:
- Validates data before passing to service layer
- Formats responses for API consumers
- Detailed logging at controller level
- Proper exception propagation

### 3. Routes Layer (`routes/routes_positions_profile.py`)

**Purpose**: Defines FastAPI endpoints with proper HTTP methods

**Endpoints Implemented**:

#### POST `/positions_profile/`
- **Description**: Create a new profile position
- **Request Body**: `ProfilePositionCreate`
- **Response**: `ProfilePosition` (201 Created)
- **Example**:
```json
{
  "profile_info": {
    "title": "Senior Developer",
    "skills": ["Python", "FastAPI"],
    "experience_years": 5
  },
  "created_by": "<EMAIL>"
}
```

#### GET `/positions_profile/{profile_id}`
- **Description**: Retrieve a specific profile position
- **Path Parameter**: `profile_id` (UUID)
- **Response**: `ProfilePosition`
- **Errors**: 404 if not found

#### PUT `/positions_profile/{profile_id}`
- **Description**: Update an existing profile position
- **Path Parameter**: `profile_id` (UUID)
- **Request Body**: `ProfilePositionUpdate`
- **Response**: `ProfilePosition`
- **Validation**: Ensures path ID matches body ID
- **Errors**: 400 if IDs don't match, 404 if not found

#### DELETE `/positions_profile/{profile_id}`
- **Description**: Soft delete a profile position
- **Path Parameter**: `profile_id` (UUID)
- **Query Parameter**: `deleted_by` (optional)
- **Response**: Success message
- **Errors**: 404 if not found

#### GET `/positions_profile/`
- **Description**: List all profile positions with pagination
- **Query Parameters**:
  - `page` (int, default: 1, min: 1)
  - `page_size` (int, default: 10, min: 1, max: 100)
  - `profile_id` (str, optional) - Filter by ID
  - `is_active` (bool, optional) - Filter by active status
  - `created_by` (str, optional) - Filter by creator
- **Response**: `ProfilePositionResponse` (paginated)

### 4. Models (`models/positions_profile.py`)

**Pydantic Models** (already existed):

- `ProfilePosition` - Full profile position model
- `ProfilePositionCreate` - Model for creation requests
- `ProfilePositionUpdate` - Model for update requests
- `ProfilePositionSearch` - Model for search filters
- `ProfilePositionResponse` - Paginated response model

### 5. Main Application (`main.py`)

**Router Registration**:
```python
from routes.routes_positions_profile import router as api_router_positions_profile

app.include_router(
    api_router_positions_profile, 
    prefix="/positions_profile", 
    tags=["positions_profile"]
)
```

## API Usage Examples

### Create Profile Position
```bash
curl -X POST "http://localhost:8080/positions_profile/" \
  -H "Content-Type: application/json" \
  -d '{
    "profile_info": {
      "title": "Full Stack Developer",
      "department": "Engineering",
      "level": "Senior"
    },
    "created_by": "<EMAIL>"
  }'
```

### Get Profile Position by ID
```bash
curl -X GET "http://localhost:8080/positions_profile/{profile_id}"
```

### Update Profile Position
```bash
curl -X PUT "http://localhost:8080/positions_profile/{profile_id}" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "{profile_id}",
    "profile_info": {
      "title": "Lead Full Stack Developer",
      "department": "Engineering",
      "level": "Lead"
    },
    "updated_by": "<EMAIL>"
  }'
```

### Delete Profile Position
```bash
curl -X DELETE "http://localhost:8080/positions_profile/{profile_id}?deleted_by=<EMAIL>"
```

### List All Profile Positions (Paginated)
```bash
# Basic pagination
curl -X GET "http://localhost:8080/positions_profile/?page=1&page_size=10"

# With filters
curl -X GET "http://localhost:8080/positions_profile/?page=1&page_size=10&is_active=true&created_by=<EMAIL>"
```

## Key Features

### 1. Soft Delete
- Profiles are never physically deleted from the database
- `is_deleted` flag is set to `true`
- `is_active` is set to `false`
- Deleted profiles are excluded from all queries

### 2. Pagination
- Default page size: 10 items
- Maximum page size: 100 items
- Returns total count for UI pagination controls

### 3. Filtering
- Filter by profile ID
- Filter by active status
- Filter by creator
- Extensible for additional filters

### 4. Error Handling
- Comprehensive try-catch blocks at all layers
- Proper HTTP status codes (404, 400, 500)
- Detailed error messages for debugging
- Logging at each layer

### 5. Logging
- Request logging at route level
- Operation logging at controller level
- Database operation logging at service level
- Error logging throughout

### 6. Type Safety
- Full type hints throughout
- Pydantic models for validation
- IDE autocomplete support

## Testing Recommendations

### Unit Tests
```python
# Test service layer
def test_create_profile_position():
    profile_data = ProfilePositionCreate(
        profile_info={"title": "Test"},
        created_by="<EMAIL>"
    )
    result = create_profile_position(profile_data)
    assert result.id is not None
    assert result.profile_info == {"title": "Test"}
```

### Integration Tests
```python
# Test full endpoint
def test_create_endpoint(client):
    response = client.post(
        "/positions_profile/",
        json={
            "profile_info": {"title": "Test"},
            "created_by": "<EMAIL>"
        }
    )
    assert response.status_code == 201
    assert "id" in response.json()
```

## Performance Considerations

1. **Database Indexing**: Consider adding indexes on frequently queried fields:
   ```sql
   CREATE INDEX idx_positions_profile_is_deleted ON positions_profile(is_deleted);
   CREATE INDEX idx_positions_profile_created_by ON positions_profile(created_by);
   CREATE INDEX idx_positions_profile_is_active ON positions_profile(is_active);
   ```

2. **Connection Pooling**: The `get_cursor()` context manager handles connections efficiently

3. **Pagination**: Limits query results to prevent memory issues

## Security Considerations

1. **SQL Injection Prevention**: Uses parameterized queries throughout
2. **Input Validation**: Pydantic models validate all inputs
3. **Soft Delete**: Preserves data for audit trails
4. **User Tracking**: Records who created/updated/deleted each profile

## Monitoring

The implementation includes:
- OpenTelemetry tracing support
- Detailed logging at DEBUG level
- Error tracking with stack traces
- Request/response logging

## Future Enhancements

Potential improvements:
1. Add bulk operations (create/update/delete multiple)
2. Add search functionality (full-text search on profile_info)
3. Add export functionality (CSV, Excel)
4. Add audit log table for tracking all changes
5. Add caching layer for frequently accessed profiles
6. Add rate limiting for API endpoints

## Conclusion

This implementation provides a complete, production-ready CRUD service for positions_profile with:
- ✅ Full CRUD operations
- ✅ Proper error handling
- ✅ Comprehensive logging
- ✅ Type safety
- ✅ Pagination and filtering
- ✅ Soft delete functionality
- ✅ Following existing codebase patterns
- ✅ RESTful API design
- ✅ OpenAPI documentation (via FastAPI)

