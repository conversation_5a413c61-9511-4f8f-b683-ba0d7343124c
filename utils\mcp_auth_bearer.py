"""
FastAPI dependency for MCP JWT authentication.

This module provides a FastAPI security dependency specifically for
authenticating MCP server requests using JWT tokens. This is separate
from the Azure AD authentication used for user requests.
"""

import logging
from typing import Optional
from fastapi import Request, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from utils.mcp_jwt_utils import verify_mcp_token, validate_mcp_jwt_config

logger = logging.getLogger(__name__)


class MCPJWTBearer(HTTPBearer):
    """
    FastAPI dependency for MCP JWT authentication.
    
    This class validates JWT tokens specifically for MCP service-to-service
    communication. It is separate from the JWTBearer class used for Azure AD
    user authentication.
    
    Usage:
        @router.post("/mcp-endpoint", dependencies=[Depends(MCPJWTBearer())])
        async def mcp_endpoint():
            # Your endpoint logic here
            pass
    """
    
    def __init__(self, auto_error: bool = True):
        """
        Initialize the MCP JWT Bearer dependency.
        
        Args:
            auto_error: If True, automatically raise HTTPException on auth failure.
                       If False, return None on auth failure.
        """
        super(MCPJWTBearer, self).__init__(auto_error=auto_error)
        
        # Validate configuration on initialization
        if not validate_mcp_jwt_config():
            logger.error("MCP JWT configuration is invalid. MCP endpoints will reject all requests.")
    
    async def __call__(self, request: Request) -> Optional[str]:
        """
        Validate the JWT token from the request.
        
        Args:
            request: FastAPI Request object
            
        Returns:
            The validated token string if authentication succeeds, None if it fails
            and auto_error is False
            
        Raises:
            HTTPException: If authentication fails and auto_error is True
        """
        # Get credentials from Authorization header
        credentials: HTTPAuthorizationCredentials = await super(MCPJWTBearer, self).__call__(request)
        
        if not credentials:
            logger.warning("MCP authentication failed: No credentials provided")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Missing authentication credentials. MCP endpoints require JWT authentication."
            )
        
        # Verify the scheme is Bearer
        if credentials.scheme != "Bearer":
            logger.warning(f"MCP authentication failed: Invalid scheme '{credentials.scheme}'")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Invalid authentication scheme. Expected 'Bearer' token."
            )
        
        # Verify the JWT token
        token = credentials.credentials
        if not self.verify_mcp_jwt(token):
            logger.warning("MCP authentication failed: Invalid or expired token")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Invalid or expired JWT token. Please generate a new token."
            )
        
        # Log successful authentication
        logger.info(f"MCP authentication successful for request to {request.url.path}")
        
        return token
    
    def verify_mcp_jwt(self, token: str) -> bool:
        """
        Verify the MCP JWT token.
        
        Args:
            token: JWT token string to verify
            
        Returns:
            bool: True if token is valid, False otherwise
        """
        try:
            payload = verify_mcp_token(token)
            if payload:
                # Token is valid
                service_name = payload.get("service", "unknown")
                logger.debug(f"Verified MCP JWT token for service: {service_name}")
                return True
            else:
                # Token verification failed
                return False
        except Exception as e:
            logger.error(f"Error verifying MCP JWT token: {str(e)}")
            return False


# Create a singleton instance for use as a dependency
mcp_jwt_bearer = MCPJWTBearer()


def get_mcp_jwt_bearer() -> MCPJWTBearer:
    """
    Get the MCP JWT Bearer dependency instance.
    
    Returns:
        MCPJWTBearer: The singleton instance
    """
    return mcp_jwt_bearer

