from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime


class PersonalInfo(BaseModel):
    """Personal information section of candidate profile."""
    full_name: str
    country: Optional[str] = None
    city: Optional[str] = None
    address: Optional[str] = None
    phone_number: Optional[str] = None
    email: Optional[str] = None
    linkedin_profile: Optional[str] = None
    website: Optional[str] = None


class Education(BaseModel):
    """Education entry in candidate profile."""
    institution_name: str
    degree: Optional[str] = None
    field_of_study: Optional[str] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    location: Optional[str] = None
    description: Optional[str] = None


class WorkExperience(BaseModel):
    """Work experience entry in candidate profile."""
    job_title: str
    company_name: str
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    location: Optional[str] = None
    responsibilities: Optional[List[str]] = None
    skills: Optional[List[str]] = None


class Skill(BaseModel):
    """Skill entry with proficiency information."""
    name: str
    proficiency_level: Optional[str] = None
    years_of_experience: Optional[int] = None


class Certification(BaseModel):
    """Certification entry in candidate profile."""
    name: str
    issuing_organization: Optional[str] = None
    issue_date: Optional[str] = None
    expiration_date: Optional[str] = None
    credential_id: Optional[str] = None
    credential_url: Optional[str] = None


class Language(BaseModel):
    """Language proficiency entry."""
    language: str
    proficiency_level: Optional[str] = None


class Project(BaseModel):
    """Project entry in candidate profile."""
    name: str
    description: str
    role: Optional[str] = None
    technologies_used: Optional[List[str]] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    url: Optional[str] = None


class Reference(BaseModel):
    """Reference entry in candidate profile."""
    name: str
    title: str
    company: str
    email: Optional[str] = None
    phone: Optional[str] = None
    relationship: Optional[str] = None


class CandidateResponse(BaseModel):
    """Complete candidate response structure."""
    personal_info: PersonalInfo
    summary: Optional[str] = None
    education: List[Education] = []
    work_experience: List[WorkExperience] = []
    roles: Optional[List[str]] = None
    skills: List[Skill] = []
    soft_skills: Optional[List[str]] = None
    certifications: List[Certification] = []
    languages: List[Language] = []
    projects: List[Project] = []
    references: Optional[List[Reference]] = None


class ExtractionCost(BaseModel):
    """Cost tracking for candidate extraction."""
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int
    cost: float


class ComprehensiveCandidateResult(BaseModel):
    """Complete result structure for a single candidate."""
    response: CandidateResponse
    extraction_cost: ExtractionCost


class BatchCandidateResults(BaseModel):
    """Batch processing results for multiple candidates."""
    candidates: List[ComprehensiveCandidateResult]
    total_candidates: int
    successful_transformations: int
    failed_transformations: int
    total_cost: float
    processing_time_ms: int
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())
