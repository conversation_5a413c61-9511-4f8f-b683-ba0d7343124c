# permissions_service.py
"""
Service layer for permission CRUD operations.
Handles business logic and database interactions for permission management.
"""

from contextlib import contextmanager
from typing import List, Optional, Dict, Any
from datetime import datetime
import logging
from uuid import UUID

import psycopg2
from psycopg2 import sql
from psycopg2.extras import RealDictCursor
from fastapi import HTTPException

from core.config import settings
from models.permission import (
    Permission,
    PermissionCreate,
    PermissionUpdate,
    PermissionResponse,
    PermissionFilters,
    PermissionListResponse,
    RolePermission,
    RolePermissionAssignment,
    UserPermissionCheck,
    PermissionCheckResult,
    PermissionsByCategory,
    validate_permission_data_integrity
)

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


# Database connection context manager
@contextmanager
def get_cursor():
    """
    Context manager for PostgreSQL cursor with error handling.

    Yields:
        cursor: PostgreSQL cursor with RealDictCursor factory

    Raises:
        HTTPException: If database connection fails
    """
    conn = None
    try:
        conn = psycopg2.connect(
            settings.DATABASE_URL,
            connect_timeout=120,
            options="-c statement_timeout=120000",
            keepalives_idle=30
        )
        with conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                yield cur
    except psycopg2.Error as e:
        logger.error(f"Database connection error: {e}")
        raise HTTPException(
            status_code=500, detail="Database connection failed")
    finally:
        if conn:
            conn.close()


def create_permission(permission_data: PermissionCreate) -> PermissionResponse:
    """
    Create a new permission in the database.

    Args:
        permission_data: Permission creation data

    Returns:
        PermissionResponse: Created permission data

    Raises:
        HTTPException: If permission creation fails or name already exists
    """
    try:
        # Validate permission data integrity
        is_valid, errors = validate_permission_data_integrity(
            permission_data.model_dump())
        if not is_valid:
            raise HTTPException(
                status_code=400, detail=f"Validation errors: {', '.join(errors)}")

        with get_cursor() as cur:
            # Check if permission with name already exists
            cur.execute(
                "SELECT id FROM permissions_smarthr WHERE name = %s",
                (permission_data.name,)
            )
            if cur.fetchone():
                raise HTTPException(
                    status_code=400,
                    detail=f"Permission with name {permission_data.name} already exists"
                )

            # Insert new permission
            insert_query = sql.SQL("""
                INSERT INTO permissions_smarthr (name, description, category, resource, action, is_active)
                VALUES (%s, %s, %s, %s, %s, %s)
                RETURNING id, name, description, category, resource, action, is_active, 
                         created_at, updated_at
            """)

            cur.execute(insert_query, (
                permission_data.name,
                permission_data.description,
                permission_data.category,
                permission_data.resource,
                permission_data.action,
                permission_data.is_active
            ))

            result = cur.fetchone()
            if not result:
                raise HTTPException(
                    status_code=500, detail="Failed to create permission")

            logger.info(f"Permission created successfully: {result['name']}")
            return PermissionResponse(**dict(result))

    except psycopg2.Error as e:
        logger.error(f"Database error creating permission: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to create permission")


def get_permission_by_id(permission_id: UUID) -> Optional[PermissionResponse]:
    """
    Retrieve a permission by ID.

    Args:
        permission_id: Permission UUID

    Returns:
        PermissionResponse: Permission data if found, None otherwise
    """
    try:
        with get_cursor() as cur:
            cur.execute("""
                SELECT id, name, description, category, resource, action, is_active, 
                       created_at, updated_at
                FROM permissions_smarthr 
                WHERE id = %s
            """, (str(permission_id),))

            result = cur.fetchone()
            if result:
                return PermissionResponse(**dict(result))
            return None

    except psycopg2.Error as e:
        logger.error(f"Database error retrieving permission by ID: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to retrieve permission")


def get_permission_by_name(name: str) -> Optional[PermissionResponse]:
    """
    Retrieve a permission by name.

    Args:
        name: Permission name

    Returns:
        PermissionResponse: Permission data if found, None otherwise
    """
    try:
        with get_cursor() as cur:
            cur.execute("""
                SELECT id, name, description, category, resource, action, is_active,
                       created_at, updated_at
                FROM permissions_smarthr 
                WHERE name = %s
            """, (name.lower().strip(),))

            result = cur.fetchone()
            if result:
                return PermissionResponse(**dict(result))
            return None

    except psycopg2.Error as e:
        logger.error(f"Database error retrieving permission by name: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to retrieve permission")


def update_permission(permission_id: UUID, permission_data: PermissionUpdate) -> PermissionResponse:
    """
    Update an existing permission.

    Args:
        permission_id: Permission UUID
        permission_data: Permission update data

    Returns:
        PermissionResponse: Updated permission data

    Raises:
        HTTPException: If permission not found or update fails
    """
    try:
        with get_cursor() as cur:
            # Check if permission exists
            cur.execute(
                "SELECT id FROM permissions_smarthr WHERE id = %s", (str(permission_id),))
            if not cur.fetchone():
                raise HTTPException(
                    status_code=404, detail="Permission not found")

            # Build dynamic update query
            update_fields = []
            update_values = []

            if permission_data.name is not None:
                # Check for name conflicts
                cur.execute(
                    "SELECT id FROM permissions_smarthr WHERE name = %s AND id != %s",
                    (permission_data.name, str(permission_id))
                )
                if cur.fetchone():
                    raise HTTPException(
                        status_code=400, detail="Permission name already exists")

                update_fields.append("name = %s")
                update_values.append(permission_data.name)

            if permission_data.description is not None:
                update_fields.append("description = %s")
                update_values.append(permission_data.description)

            if permission_data.category is not None:
                update_fields.append("category = %s")
                update_values.append(permission_data.category)

            if permission_data.resource is not None:
                update_fields.append("resource = %s")
                update_values.append(permission_data.resource)

            if permission_data.action is not None:
                update_fields.append("action = %s")
                update_values.append(permission_data.action)

            if permission_data.is_active is not None:
                update_fields.append("is_active = %s")
                update_values.append(permission_data.is_active)

            update_fields.append("updated_at = NOW()")

            if not update_fields:
                raise HTTPException(
                    status_code=400, detail="No fields to update")

            update_values.append(str(permission_id))

            update_query = f"""
                UPDATE permissions_smarthr 
                SET {', '.join(update_fields)}
                WHERE id = %s
                RETURNING id, name, description, category, resource, action, is_active,
                         created_at, updated_at
            """

            cur.execute(update_query, update_values)
            result = cur.fetchone()

            if not result:
                raise HTTPException(
                    status_code=500, detail="Failed to update permission")

            logger.info(f"Permission updated successfully: {result['name']}")
            return PermissionResponse(**dict(result))

    except psycopg2.Error as e:
        logger.error(f"Database error updating permission: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to update permission")


def delete_permission(permission_id: UUID) -> bool:
    """
    Delete a permission (soft delete by setting is_active to False).

    Args:
        permission_id: Permission UUID

    Returns:
        bool: True if permission was deleted successfully

    Raises:
        HTTPException: If permission not found or deletion fails
    """
    try:
        with get_cursor() as cur:
            # Check if permission exists
            cur.execute("""
                SELECT id, name FROM permissions_smarthr 
                WHERE id = %s AND is_active = TRUE
            """, (str(permission_id),))

            permission = cur.fetchone()
            if not permission:
                raise HTTPException(
                    status_code=404, detail="Permission not found or already inactive")

            # Check if permission is assigned to any roles
            cur.execute(
                "SELECT COUNT(*) as count FROM role_permissions WHERE permission_id = %s",
                (str(permission_id),)
            )
            role_count = cur.fetchone()['count']
            if role_count > 0:
                raise HTTPException(
                    status_code=400,
                    detail=f"Cannot delete permission. It is assigned to {role_count} role(s)"
                )

            # Soft delete the permission
            cur.execute("""
                UPDATE permissions_smarthr 
                SET is_active = FALSE, updated_at = NOW()
                WHERE id = %s
            """, (str(permission_id),))

            if cur.rowcount == 0:
                raise HTTPException(
                    status_code=404, detail="Permission not found")

            logger.info(
                f"Permission deactivated successfully: {permission['name']}")
            return True

    except psycopg2.Error as e:
        logger.error(f"Database error deleting permission: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to delete permission")


def list_permissions(filters: PermissionFilters) -> PermissionListResponse:
    """
    List permissions with filtering and pagination.

    Args:
        filters: Permission filtering and pagination parameters

    Returns:
        PermissionListResponse: Paginated list of permissions
    """
    try:
        with get_cursor() as cur:
            # Build WHERE clause
            where_conditions = []
            where_values = []

            if filters.name:
                where_conditions.append("name ILIKE %s")
                where_values.append(f"%{filters.name}%")

            if filters.category:
                where_conditions.append("category = %s")
                where_values.append(filters.category)

            if filters.resource:
                where_conditions.append("resource ILIKE %s")
                where_values.append(f"%{filters.resource}%")

            if filters.action:
                where_conditions.append("action ILIKE %s")
                where_values.append(f"%{filters.action}%")

            if filters.is_active is not None:
                where_conditions.append("is_active = %s")
                where_values.append(filters.is_active)

            where_clause = "WHERE " + \
                " AND ".join(where_conditions) if where_conditions else ""

            # Get total count
            count_query = f"SELECT COUNT(*) as total FROM permissions_smarthr {where_clause}"
            cur.execute(count_query, where_values)
            total = cur.fetchone()['total']

            # Calculate pagination
            offset = (filters.page - 1) * filters.page_size
            total_pages = (total + filters.page_size - 1) // filters.page_size

            # Get paginated results
            list_query = f"""
                SELECT id, name, description, category, resource, action, is_active,
                       created_at, updated_at
                FROM permissions_smarthr
                {where_clause}
                ORDER BY category ASC, name ASC
                LIMIT %s OFFSET %s
            """

            cur.execute(list_query, where_values + [filters.page_size, offset])
            results = cur.fetchall()

            permissions = [PermissionResponse(**dict(row)) for row in results]

            return PermissionListResponse(
                permissions=permissions,
                total=total,
                page=filters.page,
                page_size=filters.page_size,
                total_pages=total_pages
            )

    except psycopg2.Error as e:
        logger.error(f"Database error listing permissions: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to list permissions")


def get_permissions_by_category() -> List[PermissionsByCategory]:
    """
    Get permissions grouped by category.

    Returns:
        List[PermissionsByCategory]: Permissions grouped by category
    """
    try:
        with get_cursor() as cur:
            cur.execute("""
                SELECT id, name, description, category, resource, action, is_active,
                       created_at, updated_at
                FROM permissions_smarthr
                WHERE is_active = TRUE
                ORDER BY category ASC, name ASC
            """)

            results = cur.fetchall()

            # Group permissions by category
            categories = {}
            for row in results:
                permission = PermissionResponse(**dict(row))
                category = permission.category

                if category not in categories:
                    categories[category] = []
                categories[category].append(permission)

            # Convert to list of PermissionsByCategory
            grouped_permissions = []
            for category, permissions in categories.items():
                grouped_permissions.append(PermissionsByCategory(
                    category=category,
                    permissions=permissions
                ))

            return grouped_permissions

    except psycopg2.Error as e:
        logger.error(f"Database error getting permissions by category: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to get permissions by category")


def assign_permissions_to_role(assignment: RolePermissionAssignment) -> bool:
    """
    Assign multiple permissions to a role.

    Args:
        assignment: Role permission assignment data

    Returns:
        bool: True if assignment was successful

    Raises:
        HTTPException: If role not found or assignment fails
    """
    try:
        with get_cursor() as cur:
            # Verify role exists
            cur.execute("SELECT id FROM roles_smarthr WHERE id = %s AND is_active = TRUE", (str(
                assignment.role_id),))
            if not cur.fetchone():
                raise HTTPException(
                    status_code=404, detail="Role not found or inactive")

            # Verify all permissions exist
            permission_placeholders = ','.join(
                ['%s'] * len(assignment.permission_ids))
            cur.execute(f"""
                SELECT id FROM permissions_smarthr
                WHERE id IN ({permission_placeholders}) AND is_active = TRUE
            """, [str(pid) for pid in assignment.permission_ids])

            found_permissions = [row['id'] for row in cur.fetchall()]
            if len(found_permissions) != len(assignment.permission_ids):
                missing = set(str(pid) for pid in assignment.permission_ids) - \
                    set(str(pid) for pid in found_permissions)
                raise HTTPException(
                    status_code=404, detail=f"Permissions not found: {', '.join(missing)}")

            # Insert role-permission assignments
            for permission_id in assignment.permission_ids:
                cur.execute("""
                    INSERT INTO role_permissions (role_id, permission_id, granted_by)
                    VALUES (%s, %s, %s)
                    ON CONFLICT (role_id, permission_id) DO NOTHING
                """, (str(assignment.role_id), str(permission_id), assignment.granted_by))

            logger.info(
                f"Permissions assigned to role {assignment.role_id}: {len(assignment.permission_ids)} permissions")
            return True

    except psycopg2.Error as e:
        logger.error(f"Database error assigning permissions to role: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to assign permissions to role")


def remove_permissions_from_role(role_id: UUID, permission_ids: List[UUID]) -> bool:
    """
    Remove permissions from a role.

    Args:
        role_id: Role UUID
        permission_ids: List of permission UUIDs to remove

    Returns:
        bool: True if removal was successful

    Raises:
        HTTPException: If role not found or removal fails
    """
    try:
        with get_cursor() as cur:
            # Verify role exists
            cur.execute(
                "SELECT id FROM roles_smarthr WHERE id = %s", (str(role_id),))
            if not cur.fetchone():
                raise HTTPException(status_code=404, detail="Role not found")

            # Remove role-permission assignments
            permission_placeholders = ','.join(['%s'] * len(permission_ids))
            cur.execute(f"""
                DELETE FROM role_permissions
                WHERE role_id = %s AND permission_id IN ({permission_placeholders})
            """, [str(role_id)] + [str(pid) for pid in permission_ids])

            removed_count = cur.rowcount
            logger.info(
                f"Removed {removed_count} permissions from role {role_id}")
            return True

    except psycopg2.Error as e:
        logger.error(f"Database error removing permissions from role: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to remove permissions from role")


def check_user_permission(user_id: UUID, permission_name: str) -> PermissionCheckResult:
    """
    Check if a user has a specific permission.

    Args:
        user_id: User UUID
        permission_name: Permission name to check

    Returns:
        PermissionCheckResult: Permission check result

    Raises:
        HTTPException: If user not found or check fails
    """
    try:
        with get_cursor() as cur:
            # Get user's role and check permission
            cur.execute("""
                SELECT u.id as user_id, u.email, r.name as role_name, p.name as permission_name
                FROM users_smarthr u
                LEFT JOIN roles_smarthr r ON u.role_id = r.id
                LEFT JOIN role_permissions rp ON r.id = rp.role_id
                LEFT JOIN permissions_smarthr p ON rp.permission_id = p.id
                WHERE u.id = %s AND u.is_active = TRUE
                AND r.is_active = TRUE AND p.name = %s AND p.is_active = TRUE
            """, (str(user_id), permission_name.lower().strip()))

            result = cur.fetchone()

            # Check if user exists
            cur.execute(
                "SELECT id, email FROM users_smarthr WHERE id = %s", (str(user_id),))
            user = cur.fetchone()
            if not user:
                raise HTTPException(status_code=404, detail="User not found")

            # Get user's role name
            cur.execute("""
                SELECT r.name as role_name
                FROM users_smarthr u
                LEFT JOIN roles_smarthr r ON u.role_id = r.id
                WHERE u.id = %s
            """, (str(user_id),))
            role_result = cur.fetchone()
            role_name = role_result['role_name'] if role_result else None

            has_permission = result is not None

            return PermissionCheckResult(
                user_id=user_id,
                permission_name=permission_name,
                has_permission=has_permission,
                role_name=role_name,
                checked_at=datetime.now()
            )

    except psycopg2.Error as e:
        logger.error(f"Database error checking user permission: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to check user permission")


def get_user_permissions(user_id: UUID) -> List[PermissionResponse]:
    """
    Get all permissions for a user based on their role.

    Args:
        user_id: User UUID

    Returns:
        List[PermissionResponse]: List of user's permissions

    Raises:
        HTTPException: If user not found
    """
    try:
        with get_cursor() as cur:
            # Verify user exists
            cur.execute(
                "SELECT id FROM users_smarthr WHERE id = %s AND is_active = TRUE", (str(user_id),))
            if not cur.fetchone():
                raise HTTPException(
                    status_code=404, detail="User not found or inactive")

            # Get user's permissions through their role
            cur.execute("""
                SELECT DISTINCT p.id, p.name, p.description, p.category, p.resource, p.action,
                       p.is_active, p.created_at, p.updated_at
                FROM users_smarthr u
                JOIN roles_smarthr r ON u.role_id = r.id
                JOIN role_permissions rp ON r.id = rp.role_id
                JOIN permissions_smarthr p ON rp.permission_id = p.id
                WHERE u.id = %s AND u.is_active = TRUE
                AND r.is_active = TRUE AND p.is_active = TRUE
                ORDER BY p.category ASC, p.name ASC
            """, (str(user_id),))

            results = cur.fetchall()
            return [PermissionResponse(**dict(row)) for row in results]

    except psycopg2.Error as e:
        logger.error(f"Database error getting user permissions: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to get user permissions")


def get_role_permissions(role_id: UUID) -> List[PermissionResponse]:
    """
    Get all permissions assigned to a role.

    Args:
        role_id: Role UUID

    Returns:
        List[PermissionResponse]: List of role's permissions

    Raises:
        HTTPException: If role not found
    """
    try:
        with get_cursor() as cur:
            # Verify role exists
            cur.execute(
                "SELECT id FROM roles_smarthr WHERE id = %s", (str(role_id),))
            if not cur.fetchone():
                raise HTTPException(status_code=404, detail="Role not found")

            # Get role's permissions
            cur.execute("""
                SELECT p.id, p.name, p.description, p.category, p.resource, p.action,
                       p.is_active, p.created_at, p.updated_at
                FROM permissions_smarthr p
                JOIN role_permissions rp ON p.id = rp.permission_id
                WHERE rp.role_id = %s AND p.is_active = TRUE
                ORDER BY p.category ASC, p.name ASC
            """, (str(role_id),))

            results = cur.fetchall()
            return [PermissionResponse(**dict(row)) for row in results]

    except psycopg2.Error as e:
        logger.error(f"Database error getting role permissions: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to get role permissions")


def activate_permission(permission_id: UUID) -> PermissionResponse:
    """
    Activate a permission (set is_active to True).

    Args:
        permission_id: Permission UUID

    Returns:
        PermissionResponse: Updated permission data

    Raises:
        HTTPException: If permission not found or activation fails
    """
    try:
        with get_cursor() as cur:
            cur.execute("""
                UPDATE permissions_smarthr
                SET is_active = TRUE, updated_at = NOW()
                WHERE id = %s
                RETURNING id, name, description, category, resource, action, is_active,
                         created_at, updated_at
            """, (str(permission_id),))

            result = cur.fetchone()
            if not result:
                raise HTTPException(
                    status_code=404, detail="Permission not found")

            logger.info(f"Permission activated successfully: {result['name']}")
            return PermissionResponse(**dict(result))

    except psycopg2.Error as e:
        logger.error(f"Database error activating permission: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to activate permission")


def get_permissions_for_role_assignment(role_id: UUID) -> List[Dict[str, Any]]:
    """
    Get permissions with assignment status for a specific role.

    Args:
        role_id: Role UUID

    Returns:
        List[Dict]: Permissions with assignment status

    Raises:
        HTTPException: If role not found
    """
    try:
        with get_cursor() as cur:
            # Verify role exists
            cur.execute(
                "SELECT id FROM roles_smarthr WHERE id = %s", (str(role_id),))
            if not cur.fetchone():
                raise HTTPException(status_code=404, detail="Role not found")

            # Get all permissions with assignment status
            cur.execute("""
                SELECT p.id, p.name, p.description, p.category, p.resource, p.action,
                       p.is_active, p.created_at, p.updated_at,
                       CASE WHEN rp.role_id IS NOT NULL THEN TRUE ELSE FALSE END as is_assigned,
                       rp.granted_at, rp.granted_by
                FROM permissions_smarthr p
                LEFT JOIN role_permissions rp ON p.id = rp.permission_id AND rp.role_id = %s
                WHERE p.is_active = TRUE
                ORDER BY p.category ASC, p.name ASC
            """, (str(role_id),))

            results = cur.fetchall()
            return [dict(row) for row in results]

    except psycopg2.Error as e:
        logger.error(
            f"Database error getting permissions for role assignment: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to get permissions for role assignment")
