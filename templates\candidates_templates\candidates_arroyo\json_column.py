def format_education(education_list):
    if not education_list:
        return ""
    edu_str_list = []
    for edu in education_list:
        institution = edu.get("institution_name", "Unknown Institution")
        degree = edu.get("degree", "")
        field = edu.get("field_of_study", "")
        # We omit start/end dates or location if needed, or just keep them:
        description = edu.get("description", "")
        edu_str = f"Institution: {institution}, Degree: {degree}, Field: {field}, Details: {description}"
        edu_str_list.append(edu_str)
    return "\n".join(edu_str_list)


def format_work_experience(work_list):
    if not work_list:
        return ""
    work_str_list = []
    for job in work_list:
        title = job.get("job_title", "Unknown Title")
        company = job.get("company_name", "Unknown Company")
        responsibilities = job.get("responsibilities", [])
        resp_text = "; ".join(responsibilities) if responsibilities else ""
        work_str = f"Title: {title} at {company}. Responsibilities: {resp_text}"
        work_str_list.append(work_str)
    return "\n".join(work_str_list)


def format_skills(skills_list):
    if not skills_list:
        return ""
    skills_str_list = []
    for skill in skills_list:
        name = skill.get("name", "Unknown Skill")
        level = skill.get("proficiency_level", "")
        years = skill.get("years_of_experience", "")
        skills_str_list.append(f"{name} (Level: {level}, Experience: {years} years)")
    return ", ".join(skills_str_list)


def format_soft_skills(soft_list):
    if not soft_list:
        return ""
    soft_str_list = []
    for s in soft_list:
        name = s.get("name", "Unknown Soft Skill")
        desc = s.get("description", "")
        soft_str_list.append(f"{name}: {desc}")
    return "; ".join(soft_str_list)


def format_certifications(cert_list):
    if not cert_list:
        return ""
    cert_str_list = []
    for c in cert_list:
        name = c.get("name", "Unknown Certification")
        org = c.get("issuing_organization", "")
        cert_str_list.append(f"{name} from {org}")
    return "; ".join(cert_str_list)


def format_languages(lang_list):
    if not lang_list:
        return ""
    lang_str_list = []
    for lang in lang_list:
        language = lang.get("language", "Unknown Language")
        level = lang.get("proficiency_level", "")
        lang_str_list.append(f"{language} ({level})")
    return ", ".join(lang_str_list)


def format_projects(project_list):
    if not project_list:
        return ""
    proj_str_list = []
    for p in project_list:
        name = p.get("name", "Unknown Project")
        desc = p.get("description", "")
        role = p.get("role", "")
        techs = p.get("technologies_used", [])
        techs_str = ", ".join(techs)
        proj_str_list.append(
            f"Project: {name}, Role: {role}, Description: {desc}, Technologies: {techs_str}"
        )
    return "\n".join(proj_str_list)


def prepare_candidate_for_embedding(candidate_info: dict) -> str:
    """
    Converts structured candidate information to comprehensive markdown format for embedding.
    
    This function replaces the previous LLM-based summarization approach with a 
    comprehensive markdown converter that preserves all structured information
    while filtering out PII (Personally Identifiable Information).
    
    Args:
        candidate_info (dict): Structured candidate data from Lumus following
                             the comprehensive_candidate.py schema
    
    Returns:
        str: Comprehensive markdown representation of the candidate suitable
             for vectorization and RAG systems
    """
    try:
        # Import here to avoid circular imports
        from utils.candidate_markdown_converter import convert_candidate_to_markdown
        import logging
        
        logger = logging.getLogger(__name__)
        
        # Use the new markdown converter instead of LLM summarization
        markdown_text = convert_candidate_to_markdown(candidate_info)
        
        # Log the conversion for debugging
        logger.info(f"Converted candidate to markdown: {len(markdown_text)} characters")
        
        return markdown_text
        
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error in prepare_candidate_for_embedding: {str(e)}")
        
        # Fallback to basic text representation
        try:
            # Create a simple fallback if markdown conversion fails
            parts = []
            
            if candidate_info.get("summary"):
                parts.append(f"Summary: {candidate_info['summary']}")
            
            # Add work experience titles
            work_exp = candidate_info.get("work_experience", [])
            if work_exp:
                titles = [job.get("job_title", "") for job in work_exp if job.get("job_title")]
                if titles:
                    parts.append(f"Experience: {', '.join(titles[:3])}")
            
            # Add skills
            skills = candidate_info.get("skills", [])
            if skills:
                skill_names = []
                for skill in skills[:10]:
                    if isinstance(skill, dict):
                        skill_names.append(skill.get("name", ""))
                    else:
                        skill_names.append(str(skill))
                if skill_names:
                    parts.append(f"Skills: {', '.join([s for s in skill_names if s])}")
            
            return " | ".join(parts) if parts else "Professional candidate profile"
            
        except Exception as fallback_error:
            logger.error(f"Fallback conversion also failed: {str(fallback_error)}")
            return "Professional candidate profile"
