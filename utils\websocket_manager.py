"""
WebSocket Manager for real-time notifications
Manages WebSocket connections and broadcasts notifications to connected clients
"""

import json
import logging
from typing import Dict, Set, Optional, Any
from fastapi import WebSocket, WebSocketDisconnect
from datetime import datetime

logger = logging.getLogger(__name__)


class WebSocketManager:
    """
    Manages WebSocket connections for real-time notifications
    """

    def __init__(self):
        # Dictionary to store active connections by user_id
        self.active_connections: Dict[str, Set[WebSocket]] = {}
        # Store connection metadata
        self.connection_metadata: Dict[WebSocket, dict] = {}

    async def register(self, websocket: WebSocket, user_id: str, metadata: Optional[dict] = None):
        """
        Register a new WebSocket connection for a user

        Args:
            websocket: The WebSocket connection
            user_id: Unique identifier for the user
            metadata: Optional metadata about the connection
        """
        try:
            await websocket.accept()

            # Add connection to active connections
            if user_id not in self.active_connections:
                self.active_connections[user_id] = set()

            self.active_connections[user_id].add(websocket)

            # Store metadata
            self.connection_metadata[websocket] = {
                "user_id": user_id,
                "connected_at": datetime.now(),
                "metadata": metadata or {}
            }

            logger.info(f"WebSocket registered for user {user_id}. Total connections: {len(self.active_connections[user_id])}")

            # Send welcome message
            # await self.send_to_user(user_id, {
            #     "type": "connection_established",
            #     "message": "WebSocket connection established",
            #     "timestamp": datetime.now().isoformat(),
            #     "user_id": user_id
            # })

        except Exception as e:
            logger.error(f"Error registering WebSocket for user {user_id}: {str(e)}")
            raise

    def unregister(self, websocket: WebSocket, user_id: str):
        """
        Unregister a WebSocket connection

        Args:
            websocket: The WebSocket connection to remove
            user_id: User identifier
        """
        try:
            # Remove from active connections
            if user_id in self.active_connections:
                self.active_connections[user_id].discard(websocket)

                # Remove user entry if no more connections
                if not self.active_connections[user_id]:
                    del self.active_connections[user_id]

            # Remove metadata
            if websocket in self.connection_metadata:
                del self.connection_metadata[websocket]

            logger.info(f"WebSocket unregistered for user {user_id}")

        except Exception as e:
            logger.error(f"Error unregistering WebSocket for user {user_id}: {str(e)}")

    async def send_to_user(self, user_id: str, message: dict):
        """
        Send a message to all connections of a specific user

        Args:
            user_id: Target user identifier
            message: Message to send (will be JSON serialized)
        """
        if user_id not in self.active_connections:
            logger.warning(f"No active connections for user {user_id}")
            return

        # Get all connections for the user
        connections = list(self.active_connections[user_id])
        disconnected_connections = []

        for websocket in connections:
            try:
                await websocket.send_json(message)
                logger.debug(f"Message sent to user {user_id}: {message.get('type', 'unknown')}")

            except Exception as e:
                logger.error(f"Error sending message to user {user_id}: {str(e)}")
                disconnected_connections.append(websocket)

        # Clean up disconnected connections
        for websocket in disconnected_connections:
            self.unregister(websocket, user_id)

    async def send_to_all(self, message: dict):
        """
        Send a message to all connected users

        Args:
            message: Message to send to all users
        """
        for user_id in list(self.active_connections.keys()):
            await self.send_to_user(user_id, message)

    async def broadcast_to_users(self, user_ids: list, message: dict):
        """
        Send a message to multiple specific users

        Args:
            user_ids: List of user identifiers
            message: Message to send
        """
        for user_id in user_ids:
            await self.send_to_user(user_id, message)

    def get_connected_users(self) -> list:
        """
        Get list of currently connected user IDs

        Returns:
            List of user IDs with active connections
        """
        return list(self.active_connections.keys())

    def get_connection_count(self, user_id: str = None) -> int:
        """
        Get connection count for a user or total connections

        Args:
            user_id: Optional user ID to get specific count

        Returns:
            Number of connections
        """
        if user_id:
            return len(self.active_connections.get(user_id, set()))

        return sum(len(connections) for connections in self.active_connections.values())

    def get_connection_info(self) -> dict:
        """
        Get detailed information about all connections

        Returns:
            Dictionary with connection statistics
        """
        return {
            "total_users": len(self.active_connections),
            "total_connections": self.get_connection_count(),
            "users": {
                user_id: {
                    "connection_count": len(connections),
                    "connections": [
                        {
                            "connected_at": self.connection_metadata.get(ws, {}).get("connected_at"),
                            "metadata": self.connection_metadata.get(ws, {}).get("metadata", {})
                        }
                        for ws in connections
                    ]
                }
                for user_id, connections in self.active_connections.items()
            }
        }


# Global WebSocket manager instance
websocket_manager = WebSocketManager()

# Notification helper functions


async def push_notification(user_id: str, notification_type: str, payload: dict, priority: str = "normal"):
    """
    Send a notification to a specific user

    Args:
        user_id: Target user identifier
        notification_type: Type of notification (e.g., 'candidate_processed', 'match_found')
        payload: Notification data
        priority: Notification priority ('low', 'normal', 'high', 'urgent')
    """
    message = {
        "type": notification_type,
        "payload": payload,
        "priority": priority,
        "timestamp": datetime.now().isoformat(),
        "user_id": user_id
    }

    await websocket_manager.send_to_user(user_id, message)
    logger.info(f"Notification sent to user {user_id}: {notification_type}")


async def push_system_notification(message: str, notification_type: str = "system", priority: str = "normal"):
    """
    Send a system-wide notification to all connected users

    Args:
        message: System message
        notification_type: Type of system notification
        priority: Message priority
    """
    notification = {
        "type": notification_type,
        "payload": {
            "message": message,
            "system": True
        },
        "priority": priority,
        "timestamp": datetime.now().isoformat()
    }

    await websocket_manager.send_to_all(notification)
    logger.info(f"System notification sent: {message}")


async def push_candidate_notification(user_id: str, candidate_id: str, action: str, details: dict = None):
    """
    Send candidate-related notifications

    Args:
        user_id: Target user
        candidate_id: Candidate identifier
        action: Action performed ('created', 'updated', 'matched', 'processed')
        details: Additional details about the action
    """
    await push_notification(
        user_id=user_id,
        notification_type="candidate_update",
        payload={
            "candidate_id": candidate_id,
            "action": action,
            "details": details or {}
        },
        priority="normal"
    )


async def push_position_notification(user_id: str, position_id: str, action: str, details: dict = None):
    """
    Send position-related notifications

    Args:
        user_id: Target user
        position_id: Position identifier
        action: Action performed ('created', 'updated', 'matched')
        details: Additional details about the action
    """
    await push_notification(
        user_id=user_id,
        notification_type="position_update",
        payload={
            "position_id": position_id,
            "action": action,
            "details": details or {}
        },
        priority="normal"
    )


async def push_matching_notification(user_id: str, match_results: dict):
    """
    Send matching results notification

    Args:
        user_id: Target user
        match_results: Matching results data
    """
    await push_notification(
        user_id=user_id,
        notification_type="matching_complete",
        payload=match_results,
        priority="high"
    )


async def push_interview_notification(user_id: str, interview_id: str, action: str, details: dict = None):
    """
    Send interview-related notifications

    Args:
        user_id: Target user
        interview_id: Interview identifier
        action: Action performed ('created', 'updated', 'scheduled', 'completed')
        details: Additional details about the action
    """
    await push_notification(
        user_id=user_id,
        notification_type="interview_update",
        payload={
            "interview_id": interview_id,
            "action": action,
            "details": details or {}
        },
        priority="normal"
    )
