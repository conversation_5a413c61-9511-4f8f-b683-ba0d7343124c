# routes_recruiter.py
"""
Recruiter-specific routes for professional search and talent acquisition.
Implements the Adapter Pattern for searching across multiple external providers.
"""

from typing import List
from fastapi import APIRouter, HTTPException, Query
from opentelemetry import trace
import logging

# Import search controllers
from controllers.search_controller import (
    search_professionals_across_providers,
    get_available_providers,
    search_and_save_professionals,
    get_search_statistics
)

# Import models
from models.search_models import (
    SearchRequest,
    SearchResponse,
    SearchAndSaveRequest,
    SearchAndSaveResponse,
    ProvidersResponse,
    SearchStatistics
)

# Logging configuration
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
tracer = trace.get_tracer(__name__)

router = APIRouter()


# ================================
# Professional Search Routes (Adapter Pattern)
# ================================

@router.get("/search/professionals", response_model=SearchResponse)
async def search_professionals(
    query: str = Query(..., description="Search query string", min_length=2),
    providers: List[str] = Query(None, description="List of providers to search (e.g., linkedin)"),
    location: str = Query(None, description="Location filter"),
    skills: List[str] = Query(None, description="Skills filter"),
    experience_level: str = Query(None, description="Experience level (junior, mid, senior)"),
    limit: int = Query(10, description="Maximum results per provider", ge=1, le=50),
    created_by: str = Query("api_user", description="User who initiated the search")
):
    """
    Search for professionals across multiple external providers.
    
    This endpoint implements the Adapter Pattern to standardize responses from different sources
    like LinkedIn, Indeed, Glassdoor, etc. Each provider's data is adapted to our unified
    Professional model structure with proper source attribution and tag generation.
    
    **Features:**
    - Multi-provider search (search across multiple platforms simultaneously)
    - Source attribution (each result includes the source platform)
    - Automatic tag generation for search filtering
    - Unified data format across all providers
    - Comprehensive error handling
    
    **Example Usage:**
    ```
    GET /search/professionals?query=senior%20python%20developer&providers=linkedin&limit=10
    ```
    """
    try:
        with tracer.start_as_current_span("search_professionals") as span:
            span.set_attribute("query", query)
            span.set_attribute("providers", str(providers))
            span.set_attribute("limit", limit)
            
            logger.info(f"Professional search request - Query: {query}, Providers: {providers}")
            
            results = await search_professionals_across_providers(
                query=query,
                providers=providers,
                location=location,
                skills=skills,
                experience_level=experience_level,
                limit=limit,
                created_by=created_by
            )
            
            span.set_attribute("total_results", results.get("total_results", 0))
            logger.info(f"Search completed - Total results: {results.get('total_results', 0)}")
            
            return results
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in search professionals endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")


@router.get("/search/providers", response_model=ProvidersResponse)
async def get_search_providers():
    """
    Get list of available search providers and their status.
    
    Returns information about all configured search providers including:
    - Provider names and availability status
    - Provider types and capabilities
    - Health status and error information
    
    **Example Response:**
    ```json
    {
      "available_providers": ["linkedin", "indeed"],
      "provider_details": {
        "linkedin": {
          "name": "linkedin",
          "type": "LinkedInProvider",
          "available": true
        }
      },
      "total_providers": 2
    }
    ```
    """
    try:
        with tracer.start_as_current_span("get_search_providers"):
            logger.info("Getting available search providers")
            
            result = await get_available_providers()
            
            logger.info(f"Retrieved {result.get('total_providers', 0)} providers")
            return result
        
    except Exception as e:
        logger.error(f"Error getting providers: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get providers: {str(e)}")


@router.post("/search/professionals/save", response_model=SearchAndSaveResponse)
async def search_and_save(
    query: str = Query(..., description="Search query string", min_length=2),
    providers: List[str] = Query(None, description="List of providers to search"),
    location: str = Query(None, description="Location filter"),
    skills: List[str] = Query(None, description="Skills filter"),
    experience_level: str = Query(None, description="Experience level filter"),
    limit: int = Query(10, description="Maximum results per provider", ge=1, le=50),
    save_to_database: bool = Query(True, description="Whether to save results to database"),
    created_by: str = Query("api_user", description="User who initiated the search")
):
    """
    Search for professionals and save results to the database.
    
    This endpoint performs a professional search and optionally saves the results
    to the database for future reference. Useful for building a talent pipeline
    or maintaining a database of potential candidates.
    
    **Features:**
    - All search functionality from the basic search endpoint
    - Optional database persistence
    - Duplicate detection and handling
    - Batch processing for large result sets
    - Detailed save statistics and error reporting
    
    **Example Usage:**
    ```
    POST /search/professionals/save?query=react%20developer&save_to_database=true
    ```
    """
    try:
        with tracer.start_as_current_span("search_and_save_professionals") as span:
            span.set_attribute("query", query)
            span.set_attribute("save_to_database", save_to_database)
            span.set_attribute("limit", limit)
            
            logger.info(f"Search and save request - Query: {query}, Save: {save_to_database}")
            
            results = await search_and_save_professionals(
                query=query,
                providers=providers,
                location=location,
                skills=skills,
                experience_level=experience_level,
                limit=limit,
                created_by=created_by,
                save_to_database=save_to_database
            )
            
            span.set_attribute("saved_count", results.get("saved_count", 0))
            logger.info(f"Search and save completed - Saved: {results.get('saved_count', 0)} professionals")
            
            return results
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in search and save endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Search and save failed: {str(e)}")


@router.get("/search/statistics", response_model=SearchStatistics)
async def get_search_service_statistics():
    """
    Get search service statistics and health information.
    
    Provides comprehensive information about the search service including:
    - Overall service health status
    - Provider availability and status
    - Supported features and capabilities
    - Performance metrics and statistics
    
    **Use Cases:**
    - Health monitoring and alerting
    - Service capability discovery
    - Performance monitoring
    - Troubleshooting and diagnostics
    
    **Example Response:**
    ```json
    {
      "service_status": "healthy",
      "total_providers": 2,
      "available_providers": ["linkedin", "indeed"],
      "provider_status": {
        "linkedin": "healthy",
        "indeed": "healthy"
      },
      "supported_features": {
        "multi_provider_search": true,
        "tag_generation": true,
        "source_attribution": true
      }
    }
    ```
    """
    try:
        with tracer.start_as_current_span("get_search_statistics"):
            logger.info("Getting search service statistics")
            
            result = await get_search_statistics()
            
            logger.info(f"Retrieved statistics - Service status: {result.get('service_status', 'unknown')}")
            return result
        
    except Exception as e:
        logger.error(f"Error getting search statistics: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get statistics: {str(e)}")

