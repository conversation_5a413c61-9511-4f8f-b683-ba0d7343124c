# professionals_controller.py
from contextlib import contextmanager
from datetime import datetime
import json
import time
from typing import List, Optional
from fastapi import HTT<PERSON>Exception
import psycopg2
from psycopg2 import sql
from psycopg2.extras import Json
from core.config import settings
from models.professionals import Professional, ProfessionalFilters, Professionals, ProfessionalCreate, ProfessionalUpdate

# Telemetry Section
import logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


# DB helper to get cursor
@contextmanager
def get_cursor():
    """
    Context manager for PostgreSQL cursor with error handling.
    """
    conn = None
    try:
        conn = psycopg2.connect(
            settings.DATABASE_URL,
            connect_timeout=120,
            options="-c statement_timeout=120000",
            keepalives_idle=30
        )
        with conn:
            with conn.cursor() as cur:
                yield cur
    except psycopg2.Error as e:
        logger.error(f"Database error occurred in get_cursor: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    finally:
        if conn:
            conn.close()


# Create a new professional
def create_professional(professional: ProfessionalCreate) -> Professional:
    with get_cursor() as cur:
        query = sql.SQL(
            "INSERT INTO professionals (professional_info, to_be_embebbed, embedding, source, created_by, updated_by) "
            "VALUES (%s, %s, %s, %s, %s, %s) RETURNING id, professional_info, to_be_embebbed, embedding, source, created_by, created_at, updated_by, updated_at;"
        )
        cur.execute(
            query,
            (
                Json(professional.professional_info),
                professional.to_be_embebbed,
                professional.embedding,
                professional.source,
                professional.created_by,
                professional.created_by,
            ),
        )
        row = cur.fetchone()
        if not row:
            raise HTTPException(status_code=500, detail="Failed to create professional")
        return Professional(**row)


# Update an existing professional
def update_professional(professional: ProfessionalUpdate) -> Professional:
    with get_cursor() as cur:
        query = sql.SQL(
            "UPDATE professionals SET professional_info = %s, to_be_embebbed = %s, embedding = %s, source = %s, updated_by = %s, updated_at = NOW() "
            "WHERE id = %s RETURNING id, professional_info, to_be_embebbed, embedding, source, created_by, created_at, updated_by, updated_at;"
        )
        cur.execute(
            query,
            (
                Json(professional.professional_info),
                professional.to_be_embebbed,
                professional.embedding,
                professional.source,
                professional.updated_by,
                professional.id,
            ),
        )
        row = cur.fetchone()
        if not row:
            raise HTTPException(status_code=404, detail="Professional not found")
        return Professional(**row)


# Get a professional by ID
def get_professional_by_id(professional_id: str) -> Optional[Professional]:
    with get_cursor() as cur:
        query = sql.SQL(
            "SELECT id, professional_info, to_be_embebbed, embedding, source, created_by, created_at, updated_by, updated_at FROM professionals WHERE id = %s;"
        )
        cur.execute(query, (professional_id,))
        row = cur.fetchone()
        return Professional(**row) if row else None 


# Fetch all professionals from the database.
def fetch_all_professionals() -> Professionals:
    with get_cursor() as cur:
        cur.execute(
            """
            SELECT id, professional_info, to_be_embebbed, embedding, source, created_by, created_at, updated_by, updated_at
            FROM professionals
            ORDER BY created_at DESC
        """
        )
        rows = cur.fetchall()
        return Professionals(professionals=[Professional(**row) for row in rows])


# Get professionals with pagination and optional filters
def get_professionals_page(page: int, chunk_size: int = 20, filters: ProfessionalFilters | None = None) -> Professionals:
    if page < 1:
        raise HTTPException(status_code=400, detail="Page number must be greater than or equal to 1.")

    if chunk_size < 1 or chunk_size > 1000:
        raise HTTPException(status_code=400, detail="Chunk size must be between 1 and 1000.")

    params = []
    offset = (page - 1) * chunk_size
    if offset < 0:
        raise HTTPException(status_code=400, detail="Offset cannot be negative.")

    where_clause, params = build_where_clause(filters)
    params.append(chunk_size)
    params.append(offset)

    query = sql.SQL("""
        SELECT id, professional_info, to_be_embebbed, embedding, source, created_by, created_at, updated_by, updated_at
        FROM professionals
        {where_clause}
        ORDER BY created_at DESC
        LIMIT %s OFFSET %s
    """).format(where_clause=where_clause)

    with get_cursor() as cur:
        cur.execute(query, params)
        rows = cur.fetchall()
        return Professionals(professionals=[Professional(**row) for row in rows], total_items=get_total_professionals(filters))


# Delete a professional by ID
def delete_professional(professional_id: str) -> bool:
    with get_cursor() as cur:
        query = sql.SQL(
            "DELETE FROM professionals WHERE id = %s;"
        )
        cur.execute(query, (professional_id,))
        return cur.rowcount > 0


# Build the WHERE clause for the SQL query based on the filters provided
def build_where_clause(filters: ProfessionalFilters) -> sql.SQL:

    where_clause = sql.SQL("WHERE 1=1")  # Start with a true condition
    params = []

    if filters:
        # Search term filter (case-insensitive, on professional_info as text)
        if getattr(filters, "search_term", None) and filters.search_term.strip():
            where_clause += sql.SQL(" AND lower(professional_info::text) LIKE %s")
            params.append(f"%{filters.search_term.lower().strip()}%")

        # Status filter (active/inactive)
        if getattr(filters, "status", None) is not None:
            where_clause += sql.SQL(" AND is_active = %s")
            params.append(filters.status)

        # Source filter (exact match)
        if getattr(filters, "source", None):
            where_clause += sql.SQL(" AND source = %s")
            params.append(filters.source)

        # Created by filter (exact match)
        if getattr(filters, "created_by", None):
            where_clause += sql.SQL(" AND created_by = %s")
            params.append(filters.created_by)

        # Date range filter (created_at between start and end of days)
        created_from = getattr(filters, "created_from", None)
        created_to = getattr(filters, "created_to", None)
        if created_from and created_to:
            start_datetime = datetime.combine(created_from.date(), time.min)
            end_datetime = datetime.combine(created_to.date(), time.max)
            where_clause += sql.SQL(" AND created_at BETWEEN %s AND %s")
            params.extend([start_datetime, end_datetime])

    return where_clause, params


# Get total number of professionals, optionally filtered by the provided filters.
def get_total_professionals(filters: ProfessionalFilters | None = None) -> int:
    where_clause, params = build_where_clause(filters)
    query = sql.SQL("SELECT COUNT(*) FROM professionals {where_clause}").format(where_clause=where_clause)

    with get_cursor() as cur:
        cur.execute(query, params)
        row = cur.fetchone()

    return row[0] if row else 0
