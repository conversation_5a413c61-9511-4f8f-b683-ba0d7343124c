"""
Candidate Markdown Converter Utility

This module provides functionality to convert structured candidate data from Lumus
into comprehensive markdown format for improved vectorization and RAG performance.
Instead of creating lossy summaries, this preserves all structured information
while filtering out PII (Personally Identifiable Information).
"""

from typing import Dict, List, Optional, Any
import re
import logging


def _safe_strip(value: Any) -> str:
    """Safely strip a value that might be None or not a string."""
    if value is None:
        return ""
    return str(value).strip()

logger = logging.getLogger(__name__)


def convert_candidate_to_markdown(candidate_info: Dict[str, Any]) -> str:
    """
    Convert structured candidate data to comprehensive markdown format.

    This function takes structured candidate data and converts it to a detailed
    markdown format that preserves all professional information while filtering out PII.

    Handles both formats:
    - Raw Lumus JSON format (with nested result.response structure)
    - Processed candidate_info format (as stored in database)

    Args:
        candidate_info (Dict[str, Any]): Structured candidate data from Lumus
                                       or processed candidate_info from database

    Returns:
        str: Comprehensive markdown representation of the candidate
    """
    try:
        # Handle both raw Lumus format and processed database format
        if "result" in candidate_info and "response" in candidate_info["result"]:
            # Raw Lumus JSON format
            candidate_data = candidate_info["result"]["response"]
        else:
            # Processed database format (as stored in database)
            candidate_data = candidate_info

        markdown_sections = []
        
        # Professional Summary
        if candidate_data.get("summary"):
            summary = _filter_pii_from_text(candidate_data["summary"])
            markdown_sections.append(f"## Professional Summary\n\n{summary}\n")

        # Education
        education_md = _format_education_markdown(candidate_data.get("education", []))
        if education_md:
            markdown_sections.append(f"## Education\n\n{education_md}\n")

        # Work Experience
        work_md = _format_work_experience_markdown(candidate_data.get("work_experience", []))
        if work_md:
            markdown_sections.append(f"## Work Experience\n\n{work_md}\n")

        # Technical Skills
        skills_md = _format_skills_markdown(candidate_data.get("skills", []))
        if skills_md:
            markdown_sections.append(f"## Technical Skills\n\n{skills_md}\n")

        # Soft Skills
        soft_skills_md = _format_soft_skills_markdown(candidate_data.get("soft_skills", []))
        if soft_skills_md:
            markdown_sections.append(f"## Soft Skills\n\n{soft_skills_md}\n")

        # Certifications
        cert_md = _format_certifications_markdown(candidate_data.get("certifications", []))
        if cert_md:
            markdown_sections.append(f"## Certifications\n\n{cert_md}\n")

        # Languages
        lang_md = _format_languages_markdown(candidate_data.get("languages", []))
        if lang_md:
            markdown_sections.append(f"## Languages\n\n{lang_md}\n")

        # Projects
        projects_md = _format_projects_markdown(candidate_data.get("projects", []))
        if projects_md:
            markdown_sections.append(f"## Projects\n\n{projects_md}\n")

        # Professional Roles (if available)
        roles_md = _format_roles_markdown(candidate_data.get("roles", []))
        if roles_md:
            markdown_sections.append(f"## Professional Roles\n\n{roles_md}\n")
        
        # Location Information (filtered)
        location_md = _format_location_markdown(candidate_data.get("personal_info", {}))
        if location_md:
            markdown_sections.append(f"## Location\n\n{location_md}\n")

        return "\n".join(markdown_sections)

    except Exception as e:
        logger.error(f"Error converting candidate to markdown: {str(e)}")
        # Fallback to basic text representation
        return _create_fallback_text(candidate_data)


def _format_education_markdown(education_list: List[Dict[str, Any]]) -> str:
    """Format education entries as markdown."""
    if not education_list:
        return ""
    
    education_entries = []
    for edu in education_list:
        entry_parts = []
        
        # Institution and degree
        institution = _safe_strip(edu.get("institution_name", ""))
        degree = _safe_strip(edu.get("degree", ""))
        field = _safe_strip(edu.get("field_of_study", ""))
        
        if degree and field:
            entry_parts.append(f"**{degree} in {field}**")
        elif degree:
            entry_parts.append(f"**{degree}**")
        elif field:
            entry_parts.append(f"**{field}**")
        
        if institution:
            entry_parts.append(f"*{institution}*")
        
        # Dates
        start_date = _safe_strip(edu.get("start_date", ""))
        end_date = _safe_strip(edu.get("end_date", ""))
        if start_date or end_date:
            date_range = f"{start_date} - {end_date}".strip(" -")
            entry_parts.append(f"({date_range})")
        
        # Description (filtered for PII)
        description = _safe_strip(edu.get("description", ""))
        if description:
            filtered_desc = _filter_pii_from_text(description)
            entry_parts.append(f"\n  {filtered_desc}")
        
        if entry_parts:
            education_entries.append("- " + " ".join(entry_parts))
    
    return "\n".join(education_entries)


def _format_work_experience_markdown(work_list: List[Dict[str, Any]]) -> str:
    """Format work experience entries as markdown."""
    if not work_list:
        return ""
    
    work_entries = []
    for job in work_list:
        entry_parts = []
        
        # Job title and company
        title = _safe_strip(job.get("job_title", ""))
        company = _safe_strip(job.get("company_name", ""))
        
        if title and company:
            entry_parts.append(f"**{title}** at *{company}*")
        elif title:
            entry_parts.append(f"**{title}**")
        elif company:
            entry_parts.append(f"*{company}*")
        
        # Dates
        start_date = _safe_strip(job.get("start_date", ""))
        end_date = _safe_strip(job.get("end_date", ""))
        if start_date or end_date:
            date_range = f"{start_date} - {end_date}".strip(" -")
            entry_parts.append(f"({date_range})")
        
        # Location (city/country only, no specific addresses)
        location = _safe_strip(job.get("location", ""))
        if location:
            filtered_location = _filter_address_from_location(location)
            if filtered_location:
                entry_parts.append(f"- {filtered_location}")
        
        work_entry = "### " + " ".join(entry_parts) + "\n"
        
        # Responsibilities
        responsibilities = job.get("responsibilities", [])
        if responsibilities:
            work_entry += "\n**Responsibilities:**\n"
            for resp in responsibilities:
                filtered_resp = _filter_pii_from_text(str(resp))
                work_entry += f"- {filtered_resp}\n"
        
        # Skills used in this role
        skills = job.get("skills", [])
        if skills:
            # Handle both string and dict formats for skills
            skill_names = []
            for skill in skills:
                if isinstance(skill, dict):
                    skill_names.append(_safe_strip(skill.get("name", "")))
                else:
                    skill_names.append(_safe_strip(skill))

            if skill_names:
                work_entry += f"\n**Skills:** {', '.join([s for s in skill_names if s])}\n"
        
        work_entries.append(work_entry)
    
    return "\n".join(work_entries)


def _format_skills_markdown(skills_list: List[Dict[str, Any]]) -> str:
    """Format technical skills as markdown."""
    if not skills_list:
        return ""
    
    # Group skills by proficiency level if available
    skill_groups = {}
    ungrouped_skills = []
    
    for skill in skills_list:
        if isinstance(skill, dict):
            name = _safe_strip(skill.get("name", ""))
            level = _safe_strip(skill.get("proficiency_level", ""))
            years = skill.get("years_of_experience")
            
            if not name:
                continue
                
            skill_text = name
            if years:
                skill_text += f" ({years} years)"
            
            if level:
                if level not in skill_groups:
                    skill_groups[level] = []
                skill_groups[level].append(skill_text)
            else:
                ungrouped_skills.append(skill_text)
        else:
            # Handle simple string skills
            ungrouped_skills.append(str(skill))
    
    markdown_parts = []
    
    # Add grouped skills
    for level, skills in skill_groups.items():
        markdown_parts.append(f"**{level}:** {', '.join(skills)}")
    
    # Add ungrouped skills
    if ungrouped_skills:
        markdown_parts.append(f"**Skills:** {', '.join(ungrouped_skills)}")
    
    return "\n".join(markdown_parts)


def _format_soft_skills_markdown(soft_skills: List[Any]) -> str:
    """Format soft skills as markdown."""
    if not soft_skills:
        return ""
    
    skills = []
    for skill in soft_skills:
        if isinstance(skill, dict):
            name = _safe_strip(skill.get("name", ""))
            desc = _safe_strip(skill.get("description", ""))
            if name:
                if desc:
                    skills.append(f"**{name}:** {desc}")
                else:
                    skills.append(f"**{name}**")
        else:
            skills.append(str(skill))
    
    return ", ".join(skills)


def _format_certifications_markdown(cert_list: List[Dict[str, Any]]) -> str:
    """Format certifications as markdown."""
    if not cert_list:
        return ""
    
    cert_entries = []
    for cert in cert_list:
        entry_parts = []
        
        name = _safe_strip(cert.get("name", ""))
        org = _safe_strip(cert.get("issuing_organization", ""))
        issue_date = _safe_strip(cert.get("issue_date", ""))
        exp_date = _safe_strip(cert.get("expiration_date", ""))
        
        if name:
            entry_parts.append(f"**{name}**")
        
        if org:
            entry_parts.append(f"from *{org}*")
        
        if issue_date:
            if exp_date:
                entry_parts.append(f"({issue_date} - {exp_date})")
            else:
                entry_parts.append(f"({issue_date})")
        
        if entry_parts:
            cert_entries.append("- " + " ".join(entry_parts))
    
    return "\n".join(cert_entries)


def _format_languages_markdown(lang_list: List[Dict[str, Any]]) -> str:
    """Format languages as markdown."""
    if not lang_list:
        return ""
    
    lang_entries = []
    for lang in lang_list:
        language = _safe_strip(lang.get("language", ""))
        level = _safe_strip(lang.get("proficiency_level", ""))
        
        if language:
            if level:
                lang_entries.append(f"**{language}** ({level})")
            else:
                lang_entries.append(f"**{language}**")
    
    return ", ".join(lang_entries)


def _format_projects_markdown(project_list: List[Dict[str, Any]]) -> str:
    """Format projects as markdown."""
    if not project_list:
        return ""
    
    project_entries = []
    for project in project_list:
        entry_parts = []
        
        name = _safe_strip(project.get("name", ""))
        role = _safe_strip(project.get("role", ""))
        description = _safe_strip(project.get("description", ""))
        technologies = project.get("technologies_used", [])
        
        if name:
            if role:
                entry_parts.append(f"**{name}** ({role})")
            else:
                entry_parts.append(f"**{name}**")
        
        project_entry = "### " + " ".join(entry_parts) + "\n"
        
        if description:
            filtered_desc = _filter_pii_from_text(description)
            project_entry += f"{filtered_desc}\n"
        
        if technologies:
            # Handle both string and dict formats for technologies
            tech_names = []
            for tech in technologies:
                if isinstance(tech, dict):
                    tech_names.append(_safe_strip(tech.get("name", "")))
                else:
                    tech_names.append(_safe_strip(tech))

            if tech_names:
                project_entry += f"\n**Technologies:** {', '.join([t for t in tech_names if t])}\n"
        
        project_entries.append(project_entry)
    
    return "\n".join(project_entries)


def _format_roles_markdown(roles: List[str]) -> str:
    """Format professional roles as markdown."""
    if not roles:
        return ""
    
    return ", ".join([f"**{role}**" for role in roles if _safe_strip(role)])


def _format_location_markdown(personal_info: Dict[str, Any]) -> str:
    """Format location information (city/country only) as markdown."""
    location_parts = []
    
    city = _safe_strip(personal_info.get("city", ""))
    country = _safe_strip(personal_info.get("country", ""))
    
    if city:
        location_parts.append(city)
    if country:
        location_parts.append(country)
    
    if location_parts:
        return ", ".join(location_parts)
    
    return ""


def _filter_pii_from_text(text: str) -> str:
    """
    Filter PII from text content.

    Removes or masks:
    - Email addresses
    - Phone numbers
    - Specific addresses
    - Social Security Numbers
    - Credit card numbers
    - Personal names in common patterns
    """
    if not text:
        return ""

    # Remove email addresses
    text = re.sub(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', '[EMAIL_REDACTED]', text)

    # Remove phone numbers (various international formats)
    text = re.sub(r'(\+?\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}', '[PHONE_REDACTED]', text)
    text = re.sub(r'\b\d{3}[-.\s]?\d{3}[-.\s]?\d{4}\b', '[PHONE_REDACTED]', text)
    text = re.sub(r'\+\d{1,3}\s?\d{1,14}', '[PHONE_REDACTED]', text)

    # Remove specific address patterns (street numbers + street names)
    text = re.sub(r'\b\d+\s+[A-Za-z\s]+(Street|St|Avenue|Ave|Road|Rd|Drive|Dr|Lane|Ln|Boulevard|Blvd|Way|Place|Pl|Court|Ct)\b', '[ADDRESS_REDACTED]', text, flags=re.IGNORECASE)

    # Remove ZIP codes and postal codes
    text = re.sub(r'\b\d{5}(-\d{4})?\b', '[ZIP_REDACTED]', text)
    text = re.sub(r'\b[A-Z]\d[A-Z]\s?\d[A-Z]\d\b', '[POSTAL_REDACTED]', text)  # Canadian postal codes

    # Remove Social Security Numbers
    text = re.sub(r'\b\d{3}-\d{2}-\d{4}\b', '[SSN_REDACTED]', text)
    text = re.sub(r'\b\d{9}\b', '[ID_REDACTED]', text)

    # Remove credit card patterns
    text = re.sub(r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b', '[CARD_REDACTED]', text)

    # Remove common personal name patterns (Mr./Ms./Dr. + Name)
    text = re.sub(r'\b(Mr|Ms|Mrs|Dr|Prof)\.?\s+[A-Z][a-z]+(\s+[A-Z][a-z]+)?\b', '[NAME_REDACTED]', text)

    return text.strip()


def _filter_address_from_location(location: str) -> str:
    """
    Filter specific addresses from location, keeping only city/country level info.
    """
    if not location:
        return ""
    
    # Remove specific address patterns but keep city/country
    filtered = re.sub(r'\b\d+\s+[A-Za-z\s]+(Street|St|Avenue|Ave|Road|Rd|Drive|Dr|Lane|Ln|Boulevard|Blvd)[,\s]*', '', location, flags=re.IGNORECASE)
    
    # Remove ZIP codes
    filtered = re.sub(r'\b\d{5}(-\d{4})?\b', '', filtered)
    
    return filtered.strip().strip(',').strip()


def _create_fallback_text(candidate_info: Dict[str, Any]) -> str:
    """
    Create a basic fallback text representation if markdown conversion fails.
    """
    try:
        parts = []

        if candidate_info.get("summary"):
            parts.append(f"Summary: {_filter_pii_from_text(candidate_info['summary'])}")

        # Add basic work experience
        work_exp = candidate_info.get("work_experience", [])
        if work_exp:
            work_titles = [job.get("job_title", "") for job in work_exp if job.get("job_title")]
            if work_titles:
                parts.append(f"Experience: {', '.join(work_titles[:3])}")

        # Add basic skills
        skills = candidate_info.get("skills", [])
        if skills:
            skill_names = []
            for skill in skills[:10]:  # Limit to first 10 skills
                if isinstance(skill, dict):
                    skill_names.append(skill.get("name", ""))
                else:
                    skill_names.append(str(skill))
            if skill_names:
                parts.append(f"Skills: {', '.join([s for s in skill_names if s])}")

        return " | ".join(parts) if parts else "Professional candidate profile"

    except Exception as e:
        logger.error(f"Error creating fallback text: {str(e)}")
        return "Professional candidate profile"


def validate_markdown_output(markdown_text: str) -> Dict[str, Any]:
    """
    Validate that the markdown output doesn't contain PII and is properly formatted.

    Args:
        markdown_text (str): The generated markdown text

    Returns:
        Dict[str, Any]: Validation results with warnings and errors
    """
    validation_result = {
        "is_valid": True,
        "warnings": [],
        "errors": [],
        "pii_detected": [],
        "sections_found": []
    }

    if not markdown_text or not markdown_text.strip():
        validation_result["is_valid"] = False
        validation_result["errors"].append("Empty or null markdown output")
        return validation_result

    # Check for potential PII leaks
    pii_patterns = {
        "email": r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
        "phone": r'(\+?\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}',
        "ssn": r'\b\d{3}-\d{2}-\d{4}\b',
        "address": r'\b\d+\s+[A-Za-z\s]+(Street|St|Avenue|Ave|Road|Rd|Drive|Dr|Lane|Ln|Boulevard|Blvd)\b'
    }

    for pii_type, pattern in pii_patterns.items():
        matches = re.findall(pattern, markdown_text, re.IGNORECASE)
        if matches:
            validation_result["pii_detected"].append({
                "type": pii_type,
                "matches": matches
            })
            validation_result["is_valid"] = False

    # Check for expected markdown sections
    expected_sections = [
        "Professional Summary", "Education", "Work Experience",
        "Technical Skills", "Soft Skills", "Certifications",
        "Languages", "Projects", "Professional Roles", "Location"
    ]

    for section in expected_sections:
        if f"## {section}" in markdown_text:
            validation_result["sections_found"].append(section)

    # Check markdown formatting
    if not re.search(r'^##\s+', markdown_text, re.MULTILINE):
        validation_result["warnings"].append("No markdown headers found")

    # Check for minimum content length
    if len(markdown_text.strip()) < 50:
        validation_result["warnings"].append("Markdown output seems too short")

    return validation_result


def get_candidate_info_stats(candidate_info: Dict[str, Any]) -> Dict[str, Any]:
    """
    Get statistics about the candidate information for debugging and validation.

    Args:
        candidate_info (Dict[str, Any]): The candidate information dictionary

    Returns:
        Dict[str, Any]: Statistics about the candidate data
    """
    stats = {
        "total_sections": 0,
        "sections_with_data": [],
        "sections_empty": [],
        "total_work_experience": 0,
        "total_education": 0,
        "total_skills": 0,
        "total_certifications": 0,
        "total_projects": 0,
        "has_summary": False
    }

    # Check each section
    sections_to_check = [
        ("summary", "has_summary"),
        ("education", "total_education"),
        ("work_experience", "total_work_experience"),
        ("skills", "total_skills"),
        ("certifications", "total_certifications"),
        ("projects", "total_projects"),
        ("soft_skills", None),
        ("languages", None),
        ("roles", None),
        ("personal_info", None)
    ]

    for section_name, stat_key in sections_to_check:
        section_data = candidate_info.get(section_name)

        if section_data:
            if isinstance(section_data, list):
                if section_data:  # Non-empty list
                    stats["sections_with_data"].append(section_name)
                    if stat_key:
                        stats[stat_key] = len(section_data)
                else:
                    stats["sections_empty"].append(section_name)
            elif isinstance(section_data, (str, dict)):
                if section_data:  # Non-empty string or dict
                    stats["sections_with_data"].append(section_name)
                    if stat_key == "has_summary":
                        stats["has_summary"] = True
                else:
                    stats["sections_empty"].append(section_name)
        else:
            stats["sections_empty"].append(section_name)

    stats["total_sections"] = len(stats["sections_with_data"])

    return stats
