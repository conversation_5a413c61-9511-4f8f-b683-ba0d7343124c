from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field


class ProfilePosition(BaseModel):
    id: Optional[str] = Field(
        None, description="Unique identifier for the profile.")
    profile_info: dict = Field(
        None, description="Information about the profile.")
    reason: Optional[str] = Field(
        None, description="Reason for adding the profile.")
    is_active: Optional[bool] = Field(
        None, description="Indicates if the profile is active.")
    is_deleted: Optional[bool] = Field(
        None, description="Indicates if the profile is deleted.")
    created_at: Optional[datetime] = Field(
        None, description="Timestamp when the profile was created.")
    created_by: Optional[str] = Field(
        None, description="User who created the profile.")
    updated_at: Optional[datetime] = Field(
        None, description="Timestamp when the profile was last updated.")
    updated_by: Optional[str] = Field(
        None, description="User who last updated the profile.")

    class Config:
        from_attributes = True
        validate_by_name = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class ProfilePositionCreate(BaseModel):
    profile_info: dict = Field(None, description="Information about the profile.")
    created_by: Optional[str] = Field(
        None, description="User who created the profile.")


class ProfilePositionUpdate(BaseModel):
    id: str
    profile_info: dict = Field(None, description="Information about the profile.")
    updated_by: Optional[str] = Field(
        None, description="User who last updated the profile.")


class ProfilePositionSearch(BaseModel):
    profile_id: Optional[str] = Field(
        None, description="Profile identifier to search for.")
    profile_info: Optional[dict] = Field(
        None, description="Information about the profile to search for.")
    is_active: Optional[bool] = Field(
        None, description="Indicates if the profile is active.")
    is_deleted: Optional[bool] = Field(
        None, description="Indicates if the profile is deleted.")
    created_by: Optional[str] = Field(
        None, description="User who created the profile.")


class ProfilePositionResponse(BaseModel):
    total_items: int = Field(..., description="Total number of profiles.")
    items: List[ProfilePosition] = Field(..., description="List of profiles.")
