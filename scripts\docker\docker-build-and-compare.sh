#!/bin/bash

# Docker Build and Size Comparison Script
# This script builds the optimized Docker image and compares sizes

set -e

echo "🚀 Building Optimized Docker Image for SmartHR Backend"
echo "======================================================"

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Build the optimized image
echo -e "\n${BLUE}📦 Building optimized image...${NC}"
docker build -t smarthr-backend:optimized .

# Get image size
echo -e "\n${BLUE}📊 Analyzing image size...${NC}"
OPTIMIZED_SIZE=$(docker images smarthr-backend:optimized --format "{{.Size}}")

echo -e "\n${GREEN}✅ Build Complete!${NC}"
echo "======================================================"
echo -e "Image: ${YELLOW}smarthr-backend:optimized${NC}"
echo -e "Size:  ${YELLOW}${OPTIMIZED_SIZE}${NC}"
echo "======================================================"

# Show layer breakdown
echo -e "\n${BLUE}📋 Layer Breakdown:${NC}"
docker history smarthr-backend:optimized --human=true --format "table {{.CreatedBy}}\t{{.Size}}" | head -20

# Test the image
echo -e "\n${BLUE}🧪 Testing the image...${NC}"
echo "Starting container..."

# Run a quick test
docker run --rm smarthr-backend:optimized python -c "
import sys
print('Python version:', sys.version)

# Test critical imports
try:
    import fastapi
    print('✅ FastAPI imported')
except ImportError as e:
    print('❌ FastAPI import failed:', e)
    sys.exit(1)

try:
    import psycopg2
    print('✅ psycopg2 imported')
except ImportError as e:
    print('❌ psycopg2 import failed:', e)
    sys.exit(1)

try:
    import reportlab
    print('✅ reportlab imported')
except ImportError as e:
    print('❌ reportlab import failed:', e)
    sys.exit(1)

try:
    import docx
    print('✅ python-docx imported')
except ImportError as e:
    print('❌ python-docx import failed:', e)
    sys.exit(1)

print('\\n✅ All critical imports successful!')
"

# Check wkhtmltopdf
echo -e "\n${BLUE}🔍 Checking wkhtmltopdf...${NC}"
docker run --rm smarthr-backend:optimized wkhtmltopdf -V

echo -e "\n${GREEN}✅ All tests passed!${NC}"
echo "======================================================"
echo -e "${GREEN}🎉 Optimized image is ready for deployment!${NC}"
echo "======================================================"

# Show comparison if old image exists
if docker images | grep -q "smarthr-backend.*old"; then
    echo -e "\n${BLUE}📊 Size Comparison:${NC}"
    docker images smarthr-backend --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
fi

# Show recommendations
echo -e "\n${YELLOW}💡 Next Steps:${NC}"
echo "1. Tag for your registry:"
echo "   docker tag smarthr-backend:optimized your-registry/smarthr-backend:latest"
echo ""
echo "2. Push to registry:"
echo "   docker push your-registry/smarthr-backend:latest"
echo ""
echo "3. Deploy to production:"
echo "   kubectl set image deployment/smarthr-backend smarthr-backend=your-registry/smarthr-backend:latest"
echo ""
echo "4. Monitor the deployment:"
echo "   kubectl rollout status deployment/smarthr-backend"
echo ""
