# roles_controller.py
"""
Controller layer for role management operations.
Handles HTTP request processing, validation, and response formatting.
"""

import logging
from typing import List, Dict, Any
from uuid import UUID

from fastapi import HTTPException

from models.role import (
    Role,
    RoleCreate,
    RoleUpdate,
    RoleResponse,
    RoleFilters,
    RoleListResponse,
    RoleHierarchy
)
from services.roles_service import (
    create_role,
    get_role_by_id,
    get_role_by_name,
    update_role,
    delete_role,
    list_roles,
    get_role_hierarchy,
    assign_role_to_user,
    get_users_by_role,
    get_role_permissions,
    activate_role
)

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


def create_role_controller(role_data: RoleCreate) -> RoleResponse:
    """
    Controller for creating a new role.

    Args:
        role_data: Role creation data

    Returns:
        RoleResponse: Created role data

    Raises:
        HTTPException: If role creation fails
    """
    try:
        logger.info(f"Creating role with name: {role_data.name}")
        result = create_role(role_data)
        logger.info(f"Role created successfully: {result.id}")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error creating role: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def get_role_by_id_controller(role_id: UUID) -> RoleResponse:
    """
    Controller for retrieving a role by ID.

    Args:
        role_id: Role UUID

    Returns:
        RoleResponse: Role data

    Raises:
        HTTPException: If role not found
    """
    try:
        logger.info(f"Retrieving role by ID: {role_id}")
        result = get_role_by_id(role_id)
        if not result:
            raise HTTPException(status_code=404, detail="Role not found")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error retrieving role by ID: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def get_role_by_name_controller(name: str) -> RoleResponse:
    """
    Controller for retrieving a role by name.

    Args:
        name: Role name

    Returns:
        RoleResponse: Role data

    Raises:
        HTTPException: If role not found
    """
    try:
        logger.info(f"Retrieving role by name: {name}")
        result = get_role_by_name(name)
        if not result:
            raise HTTPException(status_code=404, detail="Role not found")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error retrieving role by name: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def update_role_controller(role_id: UUID, role_data: RoleUpdate) -> RoleResponse:
    """
    Controller for updating a role.

    Args:
        role_id: Role UUID
        role_data: Role update data

    Returns:
        RoleResponse: Updated role data

    Raises:
        HTTPException: If role not found or update fails
    """
    try:
        logger.info(f"Updating role: {role_id}")
        result = update_role(role_id, role_data)
        logger.info(f"Role updated successfully: {result.id}")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error updating role: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def delete_role_controller(role_id: UUID) -> Dict[str, str]:
    """
    Controller for deleting a role.

    Args:
        role_id: Role UUID

    Returns:
        Dict[str, str]: Success message

    Raises:
        HTTPException: If role not found or deletion fails
    """
    try:
        logger.info(f"Deleting role: {role_id}")
        success = delete_role(role_id)
        if success:
            logger.info(f"Role deleted successfully: {role_id}")
            return {"message": "Role deleted successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to delete role")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error deleting role: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def list_roles_controller(filters: RoleFilters) -> RoleListResponse:
    """
    Controller for listing roles with filtering and pagination.

    Args:
        filters: Role filtering and pagination parameters

    Returns:
        RoleListResponse: Paginated list of roles

    Raises:
        HTTPException: If listing fails
    """
    try:
        logger.info(f"Listing roles with filters: page={filters.page}, page_size={filters.page_size}")
        result = list_roles(filters)
        logger.info(f"Retrieved {len(result.roles)} roles out of {result.total} total")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error listing roles: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def get_role_hierarchy_controller() -> List[RoleHierarchy]:
    """
    Controller for getting role hierarchy.

    Returns:
        List[RoleHierarchy]: Hierarchical role structure

    Raises:
        HTTPException: If hierarchy retrieval fails
    """
    try:
        logger.info("Retrieving role hierarchy")
        result = get_role_hierarchy()
        logger.info(f"Retrieved role hierarchy with {len(result)} root roles")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error retrieving role hierarchy: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def assign_role_to_user_controller(user_id: UUID, role_id: UUID, assigned_by: str) -> Dict[str, str]:
    """
    Controller for assigning a role to a user.

    Args:
        user_id: User UUID
        role_id: Role UUID
        assigned_by: Who assigned the role

    Returns:
        Dict[str, str]: Success message

    Raises:
        HTTPException: If assignment fails
    """
    try:
        logger.info(f"Assigning role {role_id} to user {user_id}")
        success = assign_role_to_user(user_id, role_id, assigned_by)
        if success:
            logger.info(f"Role assigned successfully: {role_id} to {user_id}")
            return {"message": "Role assigned successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to assign role")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error assigning role: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def get_users_by_role_controller(role_id: UUID) -> List[Dict[str, Any]]:
    """
    Controller for getting users assigned to a role.

    Args:
        role_id: Role UUID

    Returns:
        List[Dict[str, Any]]: List of users with the role

    Raises:
        HTTPException: If retrieval fails
    """
    try:
        logger.info(f"Getting users for role: {role_id}")
        result = get_users_by_role(role_id)
        logger.info(f"Retrieved {len(result)} users for role {role_id}")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error getting users by role: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def get_role_permissions_controller(role_id: UUID) -> List[Dict[str, Any]]:
    """
    Controller for getting permissions assigned to a role.

    Args:
        role_id: Role UUID

    Returns:
        List[Dict[str, Any]]: List of permissions for the role

    Raises:
        HTTPException: If retrieval fails
    """
    try:
        logger.info(f"Getting permissions for role: {role_id}")
        result = get_role_permissions(role_id)
        logger.info(f"Retrieved {len(result)} permissions for role {role_id}")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error getting role permissions: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def activate_role_controller(role_id: UUID) -> RoleResponse:
    """
    Controller for activating a role.

    Args:
        role_id: Role UUID

    Returns:
        RoleResponse: Activated role data

    Raises:
        HTTPException: If activation fails
    """
    try:
        logger.info(f"Activating role: {role_id}")
        result = activate_role(role_id)
        logger.info(f"Role activated successfully: {result.id}")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error activating role: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
