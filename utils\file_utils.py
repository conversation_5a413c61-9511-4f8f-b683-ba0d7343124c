import os
import re
from datetime import datetime
from models.candidate import Candidate
import io
from docx import Document
from reportlab.lib.pagesizes import letter
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer


# Generate a unique filename for the candidate based on their name, date, and role.
def get_filename(candidate: Candidate, file_extension: str) -> str:
    """
    Generate a unique filename for the candidate based on their name, date, and role.

    Args:
        candidate (Candidate): The candidate object.
        file_extension (str): The file extension.

    Returns:
        str: The generated filename.
    """
    filename = "[BGSF-Arroyo]_"
    cand_name = candidate.candidate_info.get("personal_info").get("full_name") if candidate.candidate_info.get("personal_info").get("full_name") else "Unknown_Candidate"
    now = datetime.now()
    now_str = now.strftime("%m%d%Y")
    roles = candidate.candidate_info.get("work_experience")

    if roles and isinstance(roles[0], dict):
        roles = roles[0].get("job_title") if roles[0].get("job_title") else roles[1].get("job_title") if len(roles) > 1 and roles[1] else ""

    if roles and isinstance(roles, str):
        now_str = f"{now_str}_{roles}"
    return sanitize_filename(f"{filename}{cand_name}_{now_str}.{file_extension}")


# Sanitize the filename to remove invalid characters and replace whitespace with underscores.
def sanitize_filename(name: str) -> str:
    """
    Sanitize filename to remove invalid characters and replace whitespace with underscores.

    Args:
        name (str): The filename to sanitize.

    Returns:
        str: The sanitized filename.
    """
    sanitized = re.sub(r'[\/\\:\*\?"<>\|]', '', name)
    sanitized = re.sub(r'\s+', '_', sanitized.strip())
    return sanitized


# Convert a DOCX file to PDF using python-docx and reportlab.
def convert_docx_to_pdf(input_path, output_dir):
    """
    Convert a DOCX file to PDF using python-docx and reportlab.

    Args:
        input_path (str): Path to the input DOCX file.
        output_dir (str): Directory to save the converted PDF.

    Returns:
        str: Path to the converted PDF file.
    """
    try:
        # Read the DOCX file
        doc = Document(input_path)

        # Create output PDF path
        output_filename = os.path.splitext(os.path.basename(input_path))[0] + ".pdf"
        output_path = os.path.join(output_dir, output_filename)

        # Create PDF using reportlab
        pdf_buffer = io.BytesIO()
        pdf_doc = SimpleDocTemplate(pdf_buffer, pagesize=letter)
        styles = getSampleStyleSheet()
        story = []

        # Extract text from DOCX and add to PDF
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():  # Only add non-empty paragraphs
                # Determine style based on paragraph formatting
                if paragraph.style.name.startswith('Heading'):
                    style = styles['Heading1']
                else:
                    style = styles['Normal']

                # Create paragraph and add to story
                p = Paragraph(paragraph.text, style)
                story.append(p)
                story.append(Spacer(1, 12))  # Add some space between paragraphs

        # Build PDF
        pdf_doc.build(story)

        # Save to file
        with open(output_path, 'wb') as f:
            f.write(pdf_buffer.getvalue())

        return output_path

    except Exception as e:
        # Log error and return None
        print(f"Error converting DOCX to PDF: {str(e)}")
        return None
