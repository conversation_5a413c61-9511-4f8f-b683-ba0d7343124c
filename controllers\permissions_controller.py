# permissions_controller.py
"""
Controller layer for permission management operations.
Handles HTTP request processing, validation, and response formatting.
"""

import logging
from typing import List, Dict, Any
from uuid import UUID

from fastapi import HTTPException

from models.permission import (
    Permission,
    PermissionCreate,
    PermissionUpdate,
    PermissionResponse,
    PermissionFilters,
    PermissionListResponse,
    PermissionsByCategory,
    RolePermissionAssignment,
    UserPermissionCheck,
    PermissionCheckResult
)
from services.permissions_service import (
    create_permission,
    get_permission_by_id,
    get_permission_by_name,
    update_permission,
    delete_permission,
    list_permissions,
    get_permissions_by_category,
    assign_permissions_to_role,
    remove_permissions_from_role,
    check_user_permission,
    get_user_permissions,
    get_role_permissions,
    activate_permission,
    get_permissions_for_role_assignment
)

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


def create_permission_controller(permission_data: PermissionCreate) -> PermissionResponse:
    """
    Controller for creating a new permission.

    Args:
        permission_data: Permission creation data

    Returns:
        PermissionResponse: Created permission data

    Raises:
        HTTPException: If permission creation fails
    """
    try:
        logger.info(f"Creating permission with name: {permission_data.name}")
        result = create_permission(permission_data)
        logger.info(f"Permission created successfully: {result.id}")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error creating permission: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def get_permission_by_id_controller(permission_id: UUID) -> PermissionResponse:
    """
    Controller for retrieving a permission by ID.

    Args:
        permission_id: Permission UUID

    Returns:
        PermissionResponse: Permission data

    Raises:
        HTTPException: If permission not found
    """
    try:
        logger.info(f"Retrieving permission by ID: {permission_id}")
        result = get_permission_by_id(permission_id)
        if not result:
            raise HTTPException(status_code=404, detail="Permission not found")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error retrieving permission by ID: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def get_permission_by_name_controller(name: str) -> PermissionResponse:
    """
    Controller for retrieving a permission by name.

    Args:
        name: Permission name

    Returns:
        PermissionResponse: Permission data

    Raises:
        HTTPException: If permission not found
    """
    try:
        logger.info(f"Retrieving permission by name: {name}")
        result = get_permission_by_name(name)
        if not result:
            raise HTTPException(status_code=404, detail="Permission not found")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error retrieving permission by name: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def update_permission_controller(permission_id: UUID, permission_data: PermissionUpdate) -> PermissionResponse:
    """
    Controller for updating a permission.

    Args:
        permission_id: Permission UUID
        permission_data: Permission update data

    Returns:
        PermissionResponse: Updated permission data

    Raises:
        HTTPException: If permission not found or update fails
    """
    try:
        logger.info(f"Updating permission: {permission_id}")
        result = update_permission(permission_id, permission_data)
        logger.info(f"Permission updated successfully: {result.id}")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error updating permission: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def delete_permission_controller(permission_id: UUID) -> Dict[str, str]:
    """
    Controller for deleting a permission.

    Args:
        permission_id: Permission UUID

    Returns:
        Dict[str, str]: Success message

    Raises:
        HTTPException: If permission not found or deletion fails
    """
    try:
        logger.info(f"Deleting permission: {permission_id}")
        success = delete_permission(permission_id)
        if success:
            logger.info(f"Permission deleted successfully: {permission_id}")
            return {"message": "Permission deleted successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to delete permission")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error deleting permission: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def list_permissions_controller(filters: PermissionFilters) -> PermissionListResponse:
    """
    Controller for listing permissions with filtering and pagination.

    Args:
        filters: Permission filtering and pagination parameters

    Returns:
        PermissionListResponse: Paginated list of permissions

    Raises:
        HTTPException: If listing fails
    """
    try:
        logger.info(f"Listing permissions with filters: page={filters.page}, page_size={filters.page_size}")
        result = list_permissions(filters)
        logger.info(f"Retrieved {len(result.permissions)} permissions out of {result.total} total")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error listing permissions: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def get_permissions_by_category_controller() -> List[PermissionsByCategory]:
    """
    Controller for getting permissions grouped by category.

    Returns:
        List[PermissionsByCategory]: Permissions grouped by category

    Raises:
        HTTPException: If retrieval fails
    """
    try:
        logger.info("Retrieving permissions by category")
        result = get_permissions_by_category()
        logger.info(f"Retrieved permissions grouped into {len(result)} categories")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error retrieving permissions by category: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def assign_permissions_to_role_controller(assignment: RolePermissionAssignment) -> Dict[str, str]:
    """
    Controller for assigning permissions to a role.

    Args:
        assignment: Role permission assignment data

    Returns:
        Dict[str, str]: Success message

    Raises:
        HTTPException: If assignment fails
    """
    try:
        logger.info(f"Assigning {len(assignment.permission_ids)} permissions to role {assignment.role_id}")
        success = assign_permissions_to_role(assignment)
        if success:
            logger.info(f"Permissions assigned successfully to role {assignment.role_id}")
            return {"message": "Permissions assigned successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to assign permissions")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error assigning permissions to role: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def remove_permissions_from_role_controller(role_id: UUID, permission_ids: List[UUID]) -> Dict[str, str]:
    """
    Controller for removing permissions from a role.

    Args:
        role_id: Role UUID
        permission_ids: List of permission UUIDs to remove

    Returns:
        Dict[str, str]: Success message

    Raises:
        HTTPException: If removal fails
    """
    try:
        logger.info(f"Removing {len(permission_ids)} permissions from role {role_id}")
        success = remove_permissions_from_role(role_id, permission_ids)
        if success:
            logger.info(f"Permissions removed successfully from role {role_id}")
            return {"message": "Permissions removed successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to remove permissions")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error removing permissions from role: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def check_user_permission_controller(user_id: UUID, permission_name: str) -> PermissionCheckResult:
    """
    Controller for checking if a user has a specific permission.

    Args:
        user_id: User UUID
        permission_name: Permission name to check

    Returns:
        PermissionCheckResult: Permission check result

    Raises:
        HTTPException: If check fails
    """
    try:
        logger.info(f"Checking permission '{permission_name}' for user {user_id}")
        result = check_user_permission(user_id, permission_name)
        logger.info(f"Permission check completed: {result.has_permission}")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error checking user permission: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def get_user_permissions_controller(user_id: UUID) -> List[PermissionResponse]:
    """
    Controller for getting all permissions for a user.

    Args:
        user_id: User UUID

    Returns:
        List[PermissionResponse]: List of user's permissions

    Raises:
        HTTPException: If retrieval fails
    """
    try:
        logger.info(f"Getting permissions for user: {user_id}")
        result = get_user_permissions(user_id)
        logger.info(f"Retrieved {len(result)} permissions for user {user_id}")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error getting user permissions: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def get_role_permissions_controller(role_id: UUID) -> List[PermissionResponse]:
    """
    Controller for getting permissions assigned to a role.

    Args:
        role_id: Role UUID

    Returns:
        List[PermissionResponse]: List of role's permissions

    Raises:
        HTTPException: If retrieval fails
    """
    try:
        logger.info(f"Getting permissions for role: {role_id}")
        result = get_role_permissions(role_id)
        logger.info(f"Retrieved {len(result)} permissions for role {role_id}")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error getting role permissions: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def activate_permission_controller(permission_id: UUID) -> PermissionResponse:
    """
    Controller for activating a permission.

    Args:
        permission_id: Permission UUID

    Returns:
        PermissionResponse: Activated permission data

    Raises:
        HTTPException: If activation fails
    """
    try:
        logger.info(f"Activating permission: {permission_id}")
        result = activate_permission(permission_id)
        logger.info(f"Permission activated successfully: {result.id}")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error activating permission: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def get_permissions_for_role_assignment_controller(role_id: UUID) -> List[Dict[str, Any]]:
    """
    Controller for getting permissions with assignment status for a role.

    Args:
        role_id: Role UUID

    Returns:
        List[Dict[str, Any]]: Permissions with assignment status

    Raises:
        HTTPException: If retrieval fails
    """
    try:
        logger.info(f"Getting permissions for role assignment: {role_id}")
        result = get_permissions_for_role_assignment(role_id)
        logger.info(f"Retrieved {len(result)} permissions with assignment status for role {role_id}")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error getting permissions for role assignment: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
