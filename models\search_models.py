"""
Pydantic models for professional search functionality.
Used by the Adapter Pattern search service and API endpoints.
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, field_validator


class SearchRequest(BaseModel):
    """Request model for professional search"""
    query: str = Field(..., description="Search query string", min_length=2, max_length=200)
    providers: Optional[List[str]] = Field(None, description="List of providers to search (e.g., ['linkedin', 'indeed'])")
    location: Optional[str] = Field(None, description="Location filter (e.g., 'San Francisco, CA')")
    skills: Optional[List[str]] = Field(None, description="Skills filter (e.g., ['Python', 'React'])")
    experience_level: Optional[str] = Field(None, description="Experience level filter ('junior', 'mid', 'senior')")
    limit: Optional[int] = Field(10, description="Maximum results per provider", ge=1, le=50)
    save_to_database: Optional[bool] = Field(False, description="Whether to save results to database")
    
    @field_validator('experience_level')
    @classmethod
    def validate_experience_level(cls, v):
        if v is not None:
            valid_levels = ['junior', 'mid', 'senior']
            if v.lower() not in valid_levels:
                raise ValueError(f'Experience level must be one of: {valid_levels}')
            return v.lower()
        return v
    
    @field_validator('providers')
    @classmethod
    def validate_providers(cls, v):
        if v is not None:
            # Remove duplicates and empty strings
            return list(set([p.strip().lower() for p in v if p.strip()]))
        return v
    
    @field_validator('skills')
    @classmethod
    def validate_skills(cls, v):
        if v is not None:
            # Remove duplicates and empty strings
            return list(set([s.strip() for s in v if s.strip()]))
        return v
    
    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "query": "senior python developer",
                "providers": ["linkedin"],
                "location": "San Francisco, CA",
                "skills": ["Python", "Django", "React"],
                "experience_level": "senior",
                "limit": 10,
                "save_to_database": False
            }
        }


class SearchResponse(BaseModel):
    """Response model for professional search"""
    professionals: List[Dict[str, Any]] = Field(..., description="List of found professionals")
    total_results: int = Field(..., description="Total number of results found")
    providers_searched: List[str] = Field(..., description="List of providers that were searched")
    provider_metadata: Dict[str, Any] = Field(..., description="Metadata from each provider")
    search_parameters: Dict[str, Any] = Field(..., description="Parameters used for the search")
    timestamp: str = Field(..., description="Timestamp when search was performed")
    
    class Config:
        from_attributes = True


class SearchAndSaveRequest(SearchRequest):
    """Extended request model for search and save operations"""
    save_to_database: bool = Field(True, description="Whether to save results to database")
    
    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "query": "senior python developer",
                "providers": ["linkedin"],
                "location": "San Francisco, CA",
                "skills": ["Python", "Django", "React"],
                "experience_level": "senior",
                "limit": 10,
                "save_to_database": True
            }
        }


class SearchAndSaveResponse(BaseModel):
    """Response model for search and save operations"""
    search_results: SearchResponse = Field(..., description="Search results")
    saved_to_database: bool = Field(..., description="Whether results were saved to database")
    saved_count: int = Field(..., description="Number of professionals saved")
    save_errors: List[str] = Field(..., description="List of save errors if any")
    
    class Config:
        from_attributes = True


class ProviderInfo(BaseModel):
    """Information about a search provider"""
    name: str = Field(..., description="Provider name")
    type: str = Field(..., description="Provider type/class name")
    available: bool = Field(..., description="Whether provider is available")
    error: Optional[str] = Field(None, description="Error message if provider is unavailable")
    
    class Config:
        from_attributes = True


class ProvidersResponse(BaseModel):
    """Response model for available providers"""
    available_providers: List[str] = Field(..., description="List of available provider names")
    provider_details: Dict[str, ProviderInfo] = Field(..., description="Detailed information about each provider")
    total_providers: int = Field(..., description="Total number of available providers")
    
    class Config:
        from_attributes = True


class SearchStatistics(BaseModel):
    """Search service statistics and health information"""
    service_status: str = Field(..., description="Overall service status")
    total_providers: int = Field(..., description="Total number of providers")
    available_providers: List[str] = Field(..., description="List of available providers")
    provider_status: Dict[str, str] = Field(..., description="Status of each provider")
    supported_features: Dict[str, Any] = Field(..., description="Supported features")
    
    class Config:
        from_attributes = True


class ProfessionalTag(BaseModel):
    """Model for professional tags"""
    tag: str = Field(..., description="Tag name")
    frequency: int = Field(..., description="How often this tag appears")
    category: Optional[str] = Field(None, description="Tag category (skill, role, industry, etc.)")
    
    class Config:
        from_attributes = True


class TagsResponse(BaseModel):
    """Response model for tag-related operations"""
    tags: List[ProfessionalTag] = Field(..., description="List of tags")
    total_tags: int = Field(..., description="Total number of tags")
    categories: List[str] = Field(..., description="Available tag categories")
    
    class Config:
        from_attributes = True


class SearchFilter(BaseModel):
    """Model for search filters"""
    field: str = Field(..., description="Field to filter on")
    operator: str = Field(..., description="Filter operator (equals, contains, in, etc.)")
    value: Any = Field(..., description="Filter value")
    
    @field_validator('operator')
    @classmethod
    def validate_operator(cls, v):
        valid_operators = ['equals', 'contains', 'in', 'not_in', 'greater_than', 'less_than']
        if v not in valid_operators:
            raise ValueError(f'Operator must be one of: {valid_operators}')
        return v
    
    class Config:
        from_attributes = True


class AdvancedSearchRequest(SearchRequest):
    """Advanced search request with custom filters"""
    filters: Optional[List[SearchFilter]] = Field(None, description="Additional custom filters")
    sort_by: Optional[str] = Field(None, description="Field to sort by")
    sort_order: Optional[str] = Field("desc", description="Sort order (asc or desc)")
    
    @field_validator('sort_order')
    @classmethod
    def validate_sort_order(cls, v):
        if v is not None and v.lower() not in ['asc', 'desc']:
            raise ValueError('Sort order must be "asc" or "desc"')
        return v.lower() if v else v
    
    class Config:
        from_attributes = True
