# Professional Search Adapter Pattern

This document describes the implementation of the Adapter Pattern for searching and inserting professional data from external sources.

## Overview

The Adapter Pattern implementation allows the SmartHR system to search for professional data across multiple external providers (LinkedIn, Indeed, Glassdoor, etc.) while maintaining a unified interface and standardized data format.

## Architecture

### Core Components

1. **Service Layer** (`services/search_service.py`)
   - Main adapter service that coordinates searches across multiple providers
   - Standardizes all provider responses into unified Professional model structure
   - Handles tag association for search filtering
   - Manages source attribution for each professional record

2. **Provider Layer** (`providers/`)
   - `base_provider.py`: Abstract base class defining the provider interface
   - `linkedin_provider.py`: LinkedIn provider implementation with mock data
   - Future providers: `indeed_provider.py`, `glassdoor_provider.py`, etc.

3. **Controller Layer** (`controllers/search_controller.py`)
   - API controller that accepts search requests
   - Handles provider selection (single or multiple providers)
   - Returns unified results from all selected providers

4. **Model Layer** (`models/search_models.py`)
   - Pydantic models for search requests and responses
   - Validation and serialization for API endpoints

## Key Features

### 1. Adapter Pattern Implementation

```python
# Base Provider Interface
class BaseProvider(ABC):
    @abstractmethod
    async def search_professionals(self, request: SearchRequest) -> ProviderResponse:
        pass
    
    @abstractmethod
    def adapt_to_professional_model(self, provider_data: Dict[str, Any]) -> Dict[str, Any]:
        pass
```

### 2. Source Attribution

Every professional record includes a `source` field indicating the platform:

```json
{
  "professional_info": {
    "source": "linkedin",
    "personal_info": {...},
    "skills": [...],
    ...
  }
}
```

### 3. Tag System

Automatic tag generation for search filtering:

```python
# Generated tags from professional data
"tags": ["qa", "senior", "manual", "automation", "java", "selenium"]
```

### 4. Unified Data Format

All providers adapt their data to match the existing Professional model structure:

```json
{
  "roles": ["QA Lead", "Senior QA Engineer"],
  "source": "linkedin",
  "tags": ["qa", "senior", "automation"],
  "skills": [
    {
      "name": "Selenium",
      "proficiency_level": "NOT DEFINED",
      "years_of_experience": 5.0
    }
  ],
  "personal_info": {
    "full_name": "John Doe",
    "email": "<EMAIL>",
    "city": "San Francisco, CA"
  },
  "work_experience": [...],
  "education": [...],
  "certifications": [...]
}
```

## API Endpoints

### 1. Search Professionals

```http
GET /recruiter/search/professionals?query=senior%20python%20developer&providers=linkedin&limit=10
```

**Parameters:**
- `query` (required): Search query string
- `providers` (optional): List of providers to search
- `location` (optional): Location filter
- `skills` (optional): Skills filter
- `experience_level` (optional): junior, mid, senior
- `limit` (optional): Maximum results per provider (1-50)

### 2. Get Available Providers

```http
GET /recruiter/search/providers
```

Returns list of available providers and their status.

### 3. Search and Save

```http
POST /recruiter/search/professionals/save
```

Search for professionals and optionally save results to database.

### 4. Service Statistics

```http
GET /recruiter/search/statistics
```

Get search service health and statistics.

## Usage Examples

### Basic Search

```python
from services.search_service import search_service

# Search across all providers
results = await search_service.search_professionals(
    query="senior python developer",
    limit=10,
    created_by="user123"
)

print(f"Found {results['total_results']} professionals")
```

### Filtered Search

```python
# Search with filters
results = await search_service.search_professionals(
    query="QA engineer",
    providers=["linkedin"],
    location="San Francisco, CA",
    skills=["Selenium", "Java"],
    experience_level="senior",
    limit=5
)
```

### Provider-Specific Search

```python
# Search only LinkedIn
results = await search_service.search_professionals(
    query="full stack developer",
    providers=["linkedin"],
    limit=10
)
```

## Adding New Providers

To add a new provider (e.g., Indeed):

1. **Create Provider Class**

```python
# providers/indeed_provider.py
class IndeedProvider(BaseProvider):
    def __init__(self):
        super().__init__("indeed")
    
    async def search_professionals(self, request: SearchRequest) -> ProviderResponse:
        # Implement Indeed API calls
        pass
    
    def adapt_to_professional_model(self, indeed_data: Dict[str, Any]) -> Dict[str, Any]:
        # Adapt Indeed data format to our Professional model
        pass
```

2. **Register Provider**

```python
# services/search_service.py
def _initialize_providers(self):
    self.providers["linkedin"] = LinkedInProvider()
    self.providers["indeed"] = IndeedProvider()  # Add new provider
```

3. **Test Integration**

```python
# Test the new provider
results = await search_service.search_professionals(
    query="software engineer",
    providers=["indeed"],
    limit=5
)
```

## Data Flow

1. **API Request** → Controller validates parameters
2. **Controller** → Calls SearchService with standardized request
3. **SearchService** → Distributes search to selected providers
4. **Providers** → Execute provider-specific searches
5. **Providers** → Adapt raw data to Professional model format
6. **SearchService** → Aggregates results from all providers
7. **SearchService** → Generates tags and metadata
8. **Controller** → Returns unified response

## Benefits

1. **Extensibility**: Easy to add new providers without changing existing code
2. **Consistency**: All providers return data in the same format
3. **Flexibility**: Can search single or multiple providers
4. **Maintainability**: Provider-specific logic is isolated
5. **Testability**: Each component can be tested independently
6. **Source Tracking**: Always know where data originated

## Configuration

### Environment Variables

```bash
# Maximum results per provider
MAXIMUM_NUMBER_OF_MATCHES=50

# Provider-specific settings (future)
LINKEDIN_API_KEY=your_key
INDEED_API_KEY=your_key
```

### Provider Settings

Each provider can have its own configuration:

```python
# providers/linkedin_provider.py
class LinkedInProvider(BaseProvider):
    def __init__(self):
        super().__init__("linkedin")
        self.api_key = os.getenv("LINKEDIN_API_KEY")
        self.rate_limit = 100  # requests per hour
```

## Testing

Run the demo script to test the implementation:

```bash
python examples/search_demo.py
```

This will demonstrate:
- Basic professional search
- Filtered search with multiple criteria
- Provider information retrieval
- Tag generation
- Adapter pattern in action

## Future Enhancements

1. **Real API Integration**: Replace mock data with actual provider APIs
2. **Caching**: Implement result caching for performance
3. **Rate Limiting**: Add rate limiting for external API calls
4. **Async Processing**: Background processing for large searches
5. **Advanced Filtering**: More sophisticated search filters
6. **Machine Learning**: AI-powered result ranking and matching
