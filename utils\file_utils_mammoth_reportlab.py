import os
import re
from datetime import datetime
from models.candidate import Candidate
import io
import mammoth
from reportlab.lib.pagesizes import letter
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleD<PERSON>Template, Paragraph, Spacer
from reportlab.lib.units import inch
from bs4 import BeautifulSoup


# Generate a unique filename for the candidate based on their name, date, and role.
def get_filename(candidate: Candidate, file_extension: str) -> str:
    """
    Generate a unique filename for the candidate based on their name, date, and role.

    Args:
        candidate (Candidate): The candidate object.
        file_extension (str): The file extension.

    Returns:
        str: The generated filename.
    """
    personal_info = candidate.candidate_info.get("personal_info", {})
    full_name = personal_info.get("full_name", "Unknown")
    
    # Get current role or first role from work experience
    current_role = "Professional"
    work_experience = candidate.candidate_info.get("work_experience", [])
    if work_experience and len(work_experience) > 0:
        current_role = work_experience[0].get("job_title", "Professional")
    
    # Sanitize the filename
    safe_name = sanitize_filename(full_name)
    safe_role = sanitize_filename(current_role)
    
    # Get current date
    current_date = datetime.now().strftime("%Y%m%d")
    
    return f"{safe_name}_{safe_role}_{current_date}.{file_extension}"


# Sanitize the filename to remove invalid characters and replace whitespace with underscores.
def sanitize_filename(name: str) -> str:
    """
    Sanitize filename to remove invalid characters and replace whitespace with underscores.

    Args:
        name (str): The filename to sanitize.

    Returns:
        str: The sanitized filename.
    """
    sanitized = re.sub(r'[\/\\:\*\?"<>\|]', '', name)
    sanitized = re.sub(r'\s+', '_', sanitized.strip())
    return sanitized


# Convert a DOCX file to PDF using mammoth and reportlab (instead of weasyprint).
def convert_docx_to_pdf_mammoth_reportlab(input_path, output_dir):
    """
    Convert a DOCX file to PDF using mammoth and reportlab.
    This method preserves formatting better than basic python-docx approach
    and doesn't require system dependencies like weasyprint.

    Args:
        input_path (str): Path to the input DOCX file.
        output_dir (str): Directory to save the converted PDF.

    Returns:
        str: Path to the converted PDF file.
    """
    try:
        # Read the DOCX file and convert to HTML
        with open(input_path, "rb") as docx_file:
            result = mammoth.convert_to_html(docx_file)
            html_content = result.html
            
        # Parse HTML with BeautifulSoup for better text extraction
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Create output PDF path
        output_filename = os.path.splitext(os.path.basename(input_path))[0] + ".pdf"
        output_path = os.path.join(output_dir, output_filename)
        
        # Create PDF using reportlab
        pdf_buffer = io.BytesIO()
        pdf_doc = SimpleDocTemplate(pdf_buffer, pagesize=letter, 
                                  leftMargin=0.75*inch, rightMargin=0.75*inch,
                                  topMargin=1*inch, bottomMargin=1*inch)
        
        # Get styles
        styles = getSampleStyleSheet()
        
        # Create custom styles
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=16,
            spaceAfter=12,
            textColor='#2c3e50'
        )
        
        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading2'],
            fontSize=14,
            spaceAfter=10,
            textColor='#34495e'
        )
        
        normal_style = ParagraphStyle(
            'CustomNormal',
            parent=styles['Normal'],
            fontSize=11,
            spaceAfter=6,
            textColor='#333333'
        )
        
        story = []
        
        # Process HTML elements
        for element in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'div', 'li']):
            text = element.get_text().strip()
            if not text:
                continue
                
            # Determine style based on element type
            if element.name in ['h1']:
                style = title_style
            elif element.name in ['h2', 'h3', 'h4', 'h5', 'h6']:
                style = heading_style
            else:
                style = normal_style
            
            # Clean up text
            clean_text = text.replace('\n', ' ').replace('\r', ' ')
            clean_text = re.sub(r'\s+', ' ', clean_text).strip()
            
            if clean_text:
                # Create paragraph and add to story
                try:
                    p = Paragraph(clean_text, style)
                    story.append(p)
                    story.append(Spacer(1, 6))  # Add some space between paragraphs
                except Exception as para_error:
                    # If paragraph creation fails, try with basic style
                    try:
                        p = Paragraph(clean_text, styles['Normal'])
                        story.append(p)
                        story.append(Spacer(1, 6))
                    except:
                        # Skip this paragraph if it still fails
                        continue
        
        # If no content was extracted, add a placeholder
        if not story:
            story.append(Paragraph("Document content could not be extracted properly.", styles['Normal']))
        
        # Build PDF
        pdf_doc.build(story)
        
        # Save to file
        with open(output_path, 'wb') as f:
            f.write(pdf_buffer.getvalue())
        
        return output_path
        
    except Exception as e:
        # Log error and return None
        print(f"Error converting DOCX to PDF with mammoth + reportlab: {str(e)}")
        return None
