# positions_profile_controller.py
"""
Controller layer for positions_profile endpoints.
<PERSON>les request/response formatting and calls service layer functions.
"""

from typing import Optional
import logging

from fastapi import HTTPException

from models.positions_profile import (
    ProfilePosition,
    ProfilePositionCreate,
    ProfilePositionUpdate,
    ProfilePositionSearch,
    ProfilePositionResponse
)
from services.positions_profile_service import (
    create_profile_position as service_create,
    get_profile_position_by_id as service_get_by_id,
    update_profile_position as service_update,
    delete_profile_position as service_delete,
    get_all_profile_positions as service_get_all
)

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


def create_profile_position(profile_data: ProfilePositionCreate) -> ProfilePosition:
    """
    Controller function to create a new profile position.

    Args:
        profile_data (ProfilePositionCreate): Profile position data to create

    Returns:
        ProfilePosition: The created profile position

    Raises:
        HTTPException: If creation fails
    """
    try:
        logger.info(
            f"Creating new profile position for user: {profile_data.created_by}")
        result = service_create(profile_data)
        logger.info(
            f"Successfully created profile position with ID: {result.id}")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Controller error creating profile position: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error creating profile position: {str(e)}"
        )


def get_profile_position_by_id(profile_id: str) -> ProfilePosition:
    """
    Controller function to retrieve a profile position by ID.

    Args:
        profile_id (str): The UUID of the profile position

    Returns:
        ProfilePosition: The requested profile position

    Raises:
        HTTPException: If profile not found or query fails
    """
    try:
        logger.info(f"Fetching profile position with ID: {profile_id}")
        result = service_get_by_id(profile_id)

        if result is None:
            logger.warning(f"Profile position not found with ID: {profile_id}")
            raise HTTPException(
                status_code=404,
                detail=f"Profile position with ID {profile_id} not found"
            )

        logger.info(
            f"Successfully retrieved profile position with ID: {profile_id}")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Controller error fetching profile position: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error fetching profile position: {str(e)}"
        )


def update_profile_position(profile_data: ProfilePositionUpdate) -> ProfilePosition:
    """
    Controller function to update an existing profile position.

    Args:
        profile_data (ProfilePositionUpdate): Profile position data to update

    Returns:
        ProfilePosition: The updated profile position

    Raises:
        HTTPException: If update fails or profile not found
    """
    try:
        logger.info(f"Updating profile position with ID: {profile_data.id}")
        result = service_update(profile_data)
        logger.info(
            f"Successfully updated profile position with ID: {profile_data.id}")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Controller error updating profile position: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error updating profile position: {str(e)}"
        )


def delete_profile_position(profile_id: str, deleted_by: Optional[str] = None) -> dict:
    """
    Controller function to delete a profile position (soft delete).

    Args:
        profile_id (str): The UUID of the profile position to delete
        deleted_by (Optional[str]): User who is deleting the profile

    Returns:
        dict: Success message

    Raises:
        HTTPException: If deletion fails or profile not found
    """
    try:
        logger.info(f"Deleting profile position with ID: {profile_id}")
        service_delete(profile_id, deleted_by)
        logger.info(
            f"Successfully deleted profile position with ID: {profile_id}")
        return {
            "message": f"Profile position {profile_id} deleted successfully",
            "deleted_by": deleted_by
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Controller error deleting profile position: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error deleting profile position: {str(e)}"
        )


def get_all_profile_positions(
    page: int = 1,
    page_size: int = 10,
    filters: Optional[ProfilePositionSearch] = None
) -> ProfilePositionResponse:
    """
    Controller function to retrieve all profile positions with pagination.

    Args:
        page (int): Page number (1-indexed)
        page_size (int): Number of items per page
        filters (Optional[ProfilePositionSearch]): Optional search filters

    Returns:
        ProfilePositionResponse: Paginated list of profile positions

    Raises:
        HTTPException: If query fails
    """
    try:
        logger.info(
            f"Fetching profile positions - Page: {page}, Size: {page_size}")
        result = service_get_all(page, page_size, filters)
        logger.info(
            f"Successfully retrieved {len(result.items)} profile positions")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Controller error fetching profile positions: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error fetching profile positions: {str(e)}"
        )
