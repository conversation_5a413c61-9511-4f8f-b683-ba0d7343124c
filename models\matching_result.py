
from datetime import datetime
from typing import Any, Dict, List
from pydantic import BaseModel, Field


class MatchingResult(BaseModel):
    position_id: str
    candidate_id: str
    info: Dict[str, Any] = Field(..., description="Position or candidate information")
    cosine_similarity: float
    analysis_data: Dict[str, Any]
    last_position_update: datetime
    last_candidate_update: datetime
    created_at: datetime
    created_by: str
    updated_at: datetime
    updated_by: str


class MatchingResultCreate(BaseModel):
    position_id: str
    candidate_id: str
    cosine_similarity: float
    analysis_data: Dict[str, Any]
    last_position_update: datetime
    last_candidate_update: datetime
    created_by: str
    updated_by: str


class CosineSimilarity(BaseModel):
    position_id: str
    candidate_id: str
    cosine_similarity: float
    created_by: str
    updated_by: str


class MatchingResults(BaseModel):
    results: List[MatchingResult] = Field(..., description="List of matching results.")
    total_items: int = Field(description='Number of items returned in the response')

    class Config:
        from_attributes = True
        validate_by_name = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class MatchedPosition(BaseModel):
    candidate_id: str = Field(..., description="Unique identifier for the candidate")
    cosine_similarity: float = Field(..., description="Cosine similarity score")
    analysis_data: Dict[str, Any] = Field(..., description="Analysis data")
    pos_clientName: str = Field(..., description="Client name")
    pos_info_positionName: str = Field(..., description="Position name")
    pos_info_roleName: str = Field(..., description="Role name")
    pos_info_seniority: str = Field(..., description="Seniority level")
    pos_positionAllocations: List[Dict[str, Any]] = Field(..., description="Position allocations")
    pos_projectName: str = Field(..., description="Project name")
    position_id: str = Field(..., description="Position ID")


class MatchedCandidate(BaseModel):
    id: str = Field(..., description="Unique identifier for the position")
    proj_id: str = Field(..., description="Project ID")
    candidate_info: dict = Field(..., description="Candidate information")
    cosine_similarity: float = Field(..., description="Cosine similarity score")
    analysis_data: Dict[str, Any] = Field(..., description="Analysis data")


class MatchingCandidatesResponse(BaseModel):
    matched_candidates: List[MatchedCandidate] = Field(..., description="List of matching results.")
    processed_position: dict = Field(..., description="Processed position information.")
    timestamp: datetime = Field(..., description="Timestamp of the response.")
    total_items: int = Field(..., description="Total number of items available.")
