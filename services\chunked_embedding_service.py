#!/usr/bin/env python3
"""
Chunked Embedding Service for Enhanced Candidate Vectorization.

This service processes chunked markdown content from Lumus responses and 
generates optimized embeddings for each chunk. This approach provides more 
accurate and granular embeddings compared to processing the entire candidate 
profile as a single unit.

Key Features:
- Processes multiple chunks per candidate for granular embeddings
- Maintains chunk relationships and metadata
- Supports both sparse and dense embeddings per chunk
- Provides chunk-level similarity search capabilities
- Aggregates chunk embeddings for candidate-level matching
- Preserves semantic relationships between chunks
"""

import logging
import asyncio
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime
import numpy as np

from utils.lumus_markdown_processor import LumusMarkdownProcessor
from utils.embedding_utils import get_embeddings

logger = logging.getLogger(__name__)


class ChunkedEmbeddingService:
    """
    Service for generating and managing chunked embeddings from Lumus responses.
    
    This service takes processed Lumus responses and creates optimized embeddings
    for each chunk, enabling more accurate candidate matching and retrieval.
    """
    
    def __init__(self):
        """Initialize the chunked embedding service."""
        self.markdown_processor = LumusMarkdownProcessor(
            chunk_size=25000,  # Large chunks - only split if content exceeds 90% of embedding model limit
            chunk_overlap=500,  # Semantic continuity
            max_chunks=2  # Maximum 2 chunks to keep it simple
        )
    
    async def process_lumus_response_with_chunks(self, lumus_response: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a full Lumus response into chunked embeddings.
        
        Args:
            lumus_response (Dict[str, Any]): Full raw Lumus API response
            
        Returns:
            Dict[str, Any]: Processed result with chunked embeddings and metadata
        """
        try:
            logger.info(f"Starting chunked processing for task: {lumus_response.get('task_id', 'unknown')}")
            
            # Step 1: Process Lumus response into markdown chunks
            markdown_result = self.markdown_processor.process_full_lumus_response(lumus_response)

            if not markdown_result['success']:
                return {
                    'success': False,
                    'error': f"Markdown processing failed: {markdown_result.get('error')}",
                    'chunks': [],
                    'embeddings': []
                }

            # Extract original data for skill analysis
            original_data = lumus_response.get('result', {}).get('response', {})
            
            # Step 2: Generate embeddings for each chunk
            chunk_embeddings = await self._generate_chunk_embeddings(markdown_result['chunks'], original_data)
            
            # Step 3: Create aggregated candidate embedding
            aggregated_embedding = self._create_aggregated_embedding(chunk_embeddings)
            
            # Step 4: Prepare final result
            result = {
                'success': True,
                'task_id': lumus_response.get('task_id'),
                'file_name': lumus_response.get('file_name'),
                'metadata': markdown_result['metadata'],
                'full_markdown': markdown_result['full_markdown'],
                'chunk_count': len(chunk_embeddings),
                'chunks': chunk_embeddings,
                'aggregated_embedding': aggregated_embedding,
                'processing_timestamp': datetime.now().isoformat(),
                'total_characters': markdown_result['total_characters']
            }
            
            logger.info(f"Successfully processed {len(chunk_embeddings)} chunks with embeddings")
            return result
            
        except Exception as e:
            logger.error(f"Error in chunked embedding processing: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'chunks': [],
                'embeddings': [],
                'processing_timestamp': datetime.now().isoformat()
            }
    
    async def _generate_chunk_embeddings(self, chunks: List[Dict[str, Any]], original_data: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        Generate embeddings for each chunk.
        
        Args:
            chunks (List[Dict[str, Any]]): List of markdown chunks
            
        Returns:
            List[Dict[str, Any]]: Chunks with their embeddings
        """
        chunk_embeddings = []
        
        for chunk in chunks:
            try:
                logger.debug(f"Generating embeddings for chunk {chunk['index']} ({chunk['size']} chars)")
                
                # Generate embeddings for this chunk
                sparse_embedding, dense_embedding = get_embeddings(chunk['text'])
                
                chunk_with_embedding = {
                    'index': chunk['index'],
                    'text': chunk['text'],
                    'size': chunk['size'],
                    'section': chunk['section'],
                    'lines': chunk['lines'],
                    'word_count': chunk['word_count'],
                    'content_type': chunk['content_type'],
                    'has_section_header': chunk['has_section_header'],
                    'sparse_embedding': sparse_embedding,
                    'dense_embedding': dense_embedding,
                    'embedding_success': dense_embedding is not None,
                    'embedding_dimensions': len(dense_embedding) if dense_embedding else 0,
                    'original_data': original_data  # Add original data for skill analysis
                }
                
                chunk_embeddings.append(chunk_with_embedding)
                
                # Small delay to avoid rate limiting
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Error generating embedding for chunk {chunk['index']}: {str(e)}")
                
                # Add chunk without embedding
                chunk_with_embedding = {
                    **chunk,
                    'sparse_embedding': None,
                    'dense_embedding': None,
                    'embedding_success': False,
                    'embedding_error': str(e),
                    'original_data': original_data  # Add original data for skill analysis
                }
                chunk_embeddings.append(chunk_with_embedding)
        
        return chunk_embeddings
    
    def _create_aggregated_embedding(self, chunk_embeddings: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Create an aggregated embedding from all chunks.
        
        This combines chunk embeddings using weighted averaging based on
        chunk importance and content type.
        
        Args:
            chunk_embeddings (List[Dict[str, Any]]): Chunks with embeddings
            
        Returns:
            Dict[str, Any]: Aggregated embedding information
        """
        try:
            # Filter chunks with successful embeddings
            valid_chunks = [chunk for chunk in chunk_embeddings if chunk.get('embedding_success', False)]
            
            if not valid_chunks:
                return {
                    'success': False,
                    'error': 'No valid embeddings to aggregate',
                    'dense_embedding': None,
                    'method': 'none'
                }
            
            # Extract dense embeddings
            dense_embeddings = [chunk['dense_embedding'] for chunk in valid_chunks]
            
            # Calculate weights based on content importance
            weights = self._calculate_chunk_weights(valid_chunks)
            
            # Weighted average of embeddings
            weighted_embeddings = []
            for embedding, weight in zip(dense_embeddings, weights):
                weighted_embedding = [val * weight for val in embedding]
                weighted_embeddings.append(weighted_embedding)
            
            # Sum and normalize
            aggregated = [sum(values) for values in zip(*weighted_embeddings)]
            
            # Normalize the aggregated embedding
            magnitude = sum(val ** 2 for val in aggregated) ** 0.5
            if magnitude > 0:
                normalized_embedding = [val / magnitude for val in aggregated]
            else:
                normalized_embedding = aggregated
            
            return {
                'success': True,
                'dense_embedding': normalized_embedding,
                'method': 'weighted_average',
                'chunk_count': len(valid_chunks),
                'weights': weights,
                'dimensions': len(normalized_embedding)
            }
            
        except Exception as e:
            logger.error(f"Error creating aggregated embedding: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'dense_embedding': None,
                'method': 'error'
            }
    
    def _calculate_chunk_weights(self, chunks: List[Dict[str, Any]]) -> List[float]:
        """
        Calculate importance weights for chunks based on content type and characteristics.
        
        Args:
            chunks (List[Dict[str, Any]]): Chunks to weight
            
        Returns:
            List[float]: Normalized weights for each chunk
        """
        weights = []
        
        # Content type importance scores
        content_type_scores = {
            'summary': 1.0,  # Professional summary is most important
            'work_experience': 0.9,  # Work experience is very important
            'skills': 0.8,  # Skills are important for matching
            'education': 0.7,  # Education is moderately important
            'projects': 0.6,  # Projects show practical experience
            'certifications': 0.5,  # Certifications add value
            'languages': 0.4,  # Languages are nice to have
            'general': 0.3  # General content is least important
        }
        
        for chunk in chunks:
            base_weight = content_type_scores.get(chunk.get('content_type', 'general'), 0.3)
            
            # Adjust weight based on chunk characteristics
            size_factor = min(chunk.get('size', 0) / 1000, 1.0)  # Larger chunks get slight boost
            word_factor = min(chunk.get('word_count', 0) / 200, 1.0)  # More words = more content
            header_factor = 1.1 if chunk.get('has_section_header', False) else 1.0  # Headers are important
            
            final_weight = base_weight * size_factor * word_factor * header_factor
            weights.append(final_weight)
        
        # Normalize weights to sum to 1
        total_weight = sum(weights)
        if total_weight > 0:
            normalized_weights = [w / total_weight for w in weights]
        else:
            normalized_weights = [1.0 / len(weights)] * len(weights)
        
        return normalized_weights
    
    def search_chunks_by_similarity(self, 
                                  query_embedding: List[float], 
                                  chunk_embeddings: List[Dict[str, Any]], 
                                  top_k: int = 5) -> List[Dict[str, Any]]:
        """
        Search chunks by embedding similarity.
        
        Args:
            query_embedding (List[float]): Query embedding vector
            chunk_embeddings (List[Dict[str, Any]]): Chunks with embeddings
            top_k (int): Number of top results to return
            
        Returns:
            List[Dict[str, Any]]: Top matching chunks with similarity scores
        """
        try:
            results = []
            
            for chunk in chunk_embeddings:
                if not chunk.get('embedding_success', False):
                    continue
                
                # Calculate cosine similarity
                chunk_embedding = chunk['dense_embedding']
                similarity = self._cosine_similarity(query_embedding, chunk_embedding)
                
                result = {
                    'chunk_index': chunk['index'],
                    'similarity_score': similarity,
                    'content_type': chunk['content_type'],
                    'section': chunk['section'],
                    'text_preview': chunk['text'][:200] + "..." if len(chunk['text']) > 200 else chunk['text'],
                    'size': chunk['size'],
                    'word_count': chunk['word_count']
                }
                results.append(result)
            
            # Sort by similarity score (descending)
            results.sort(key=lambda x: x['similarity_score'], reverse=True)
            
            return results[:top_k]
            
        except Exception as e:
            logger.error(f"Error in chunk similarity search: {str(e)}")
            return []
    
    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """Calculate cosine similarity between two vectors."""
        try:
            # Convert to numpy arrays for easier computation
            a = np.array(vec1)
            b = np.array(vec2)
            
            # Calculate cosine similarity
            dot_product = np.dot(a, b)
            norm_a = np.linalg.norm(a)
            norm_b = np.linalg.norm(b)
            
            if norm_a == 0 or norm_b == 0:
                return 0.0
            
            similarity = dot_product / (norm_a * norm_b)
            return float(similarity)
            
        except Exception as e:
            logger.error(f"Error calculating cosine similarity: {str(e)}")
            return 0.0
    
    def get_processing_stats(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get processing statistics from a chunked embedding result.
        
        Args:
            result (Dict[str, Any]): Result from process_lumus_response_with_chunks
            
        Returns:
            Dict[str, Any]: Processing statistics
        """
        if not result.get('success', False):
            return {'error': 'Processing failed'}
        
        chunks = result.get('chunks', [])
        successful_embeddings = sum(1 for chunk in chunks if chunk.get('embedding_success', False))
        
        content_type_distribution = {}
        for chunk in chunks:
            content_type = chunk.get('content_type', 'unknown')
            content_type_distribution[content_type] = content_type_distribution.get(content_type, 0) + 1
        
        return {
            'total_chunks': len(chunks),
            'successful_embeddings': successful_embeddings,
            'embedding_success_rate': successful_embeddings / len(chunks) if chunks else 0,
            'total_characters': result.get('total_characters', 0),
            'average_chunk_size': sum(chunk.get('size', 0) for chunk in chunks) / len(chunks) if chunks else 0,
            'content_type_distribution': content_type_distribution,
            'aggregated_embedding_success': result.get('aggregated_embedding', {}).get('success', False),
            'processing_time': result.get('processing_timestamp')
        }
