#!/bin/bash

# Test Docker Permissions Script
# Verifies that the optimized Docker image runs correctly with non-root user

set -e

echo "🧪 Testing Docker Image Permissions"
echo "===================================="

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Build the image
echo -e "\n${YELLOW}📦 Building Docker image...${NC}"
docker build -t smarthr-backend:test .

# Test 1: Check user permissions
echo -e "\n${YELLOW}1️⃣ Testing user permissions...${NC}"
USER_CHECK=$(docker run --rm smarthr-backend:test whoami)
if [ "$USER_CHECK" = "appuser" ]; then
    echo -e "${GREEN}✅ Running as non-root user: $USER_CHECK${NC}"
else
    echo -e "${RED}❌ ERROR: Running as $USER_CHECK instead of appuser${NC}"
    exit 1
fi

# Test 2: Check Python packages are accessible
echo -e "\n${YELLOW}2️⃣ Testing Python package access...${NC}"
docker run --rm smarthr-backend:test python -c "
import sys
import os

# Check if packages are importable
try:
    import fastapi
    print('✅ FastAPI accessible')
except ImportError as e:
    print(f'❌ FastAPI not accessible: {e}')
    sys.exit(1)

try:
    import uvicorn
    print('✅ Uvicorn accessible')
except ImportError as e:
    print(f'❌ Uvicorn not accessible: {e}')
    sys.exit(1)

try:
    import psycopg2
    print('✅ psycopg2 accessible')
except ImportError as e:
    print(f'❌ psycopg2 not accessible: {e}')
    sys.exit(1)

print('✅ All critical packages accessible')
"

# Test 3: Check uvicorn executable
echo -e "\n${YELLOW}3️⃣ Testing uvicorn executable...${NC}"
docker run --rm smarthr-backend:test which uvicorn

# Test 4: Check file permissions
echo -e "\n${YELLOW}4️⃣ Testing file permissions...${NC}"
docker run --rm smarthr-backend:test ls -la /app | head -10

# Test 5: Try to start uvicorn (with timeout)
echo -e "\n${YELLOW}5️⃣ Testing uvicorn startup...${NC}"
timeout 10s docker run --rm -p 8080:8080 smarthr-backend:test uvicorn main:app --host 0.0.0.0 --port 8080 &
UVICORN_PID=$!

# Wait a bit for startup
sleep 3

# Check if process is still running
if ps -p $UVICORN_PID > /dev/null; then
    echo -e "${GREEN}✅ Uvicorn started successfully${NC}"
    kill $UVICORN_PID 2>/dev/null || true
else
    echo -e "${RED}❌ Uvicorn failed to start${NC}"
    exit 1
fi

# Test 6: Check wkhtmltopdf
echo -e "\n${YELLOW}6️⃣ Testing wkhtmltopdf...${NC}"
docker run --rm smarthr-backend:test wkhtmltopdf -V

echo -e "\n${GREEN}✅ All permission tests passed!${NC}"
echo "===================================="
echo -e "${GREEN}🎉 Docker image is ready for deployment${NC}"
echo ""
echo "To run the container:"
echo "  docker run -p 8080:8080 smarthr-backend:test"
echo ""
echo "To use with docker-compose:"
echo "  docker-compose -f docker-compose.optimized.yml up"
echo ""
