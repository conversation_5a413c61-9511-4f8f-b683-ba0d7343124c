from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, field_validator, Field
from uuid import UUID
import re


class RoleBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    is_system_role: bool = False
    hierarchy_level: int = Field(default=0, ge=0, le=10)
    parent_role_id: Optional[UUID] = None
    is_active: bool = True


class RoleCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    is_system_role: bool = False
    hierarchy_level: int = Field(default=0, ge=0, le=10)
    parent_role_id: Optional[UUID] = None
    is_active: bool = True

    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('Role name cannot be empty')
        
        # Check for valid role name format (alphanumeric, underscores, hyphens)
        if not re.match(r'^[a-zA-Z0-9_-]+$', v.strip()):
            raise ValueError('Role name can only contain letters, numbers, underscores, and hyphens')
        
        return v.lower().strip()

    @field_validator('description')
    @classmethod
    def validate_description(cls, v):
        if v is not None and len(v.strip()) > 500:
            raise ValueError('Description cannot exceed 500 characters')
        return v.strip() if v else v

    @field_validator('hierarchy_level')
    @classmethod
    def validate_hierarchy_level(cls, v):
        if v < 0 or v > 10:
            raise ValueError('Hierarchy level must be between 0 and 10')
        return v

    def validate_system_role_name(self):
        """Validate that system roles have predefined names"""
        if self.is_system_role:
            valid_system_roles = ['system_admin', 'project_admin', 'recruiter', 'interviewer', 'read_user']
            if self.name not in valid_system_roles:
                raise ValueError(f'System role name must be one of: {", ".join(valid_system_roles)}')


class RoleUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = None
    hierarchy_level: Optional[int] = Field(None, ge=0, le=10)
    parent_role_id: Optional[UUID] = None
    is_active: Optional[bool] = None

    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        if v is not None:
            if not v or len(v.strip()) == 0:
                raise ValueError('Role name cannot be empty')
            
            # Check for valid role name format
            if not re.match(r'^[a-zA-Z0-9_-]+$', v.strip()):
                raise ValueError('Role name can only contain letters, numbers, underscores, and hyphens')
            
            return v.lower().strip()
        return v

    @field_validator('description')
    @classmethod
    def validate_description(cls, v):
        if v is not None and len(v.strip()) > 500:
            raise ValueError('Description cannot exceed 500 characters')
        return v.strip() if v else v


class RoleResponse(RoleBase):
    id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
        validate_by_name = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None,
            UUID: lambda v: str(v) if v else None
        }


class Role(RoleBase):
    id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
        validate_by_name = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None,
            UUID: lambda v: str(v) if v else None
        }


class RoleListResponse(BaseModel):
    roles: List[RoleResponse]
    total: int
    page: int
    page_size: int
    total_pages: int

    class Config:
        from_attributes = True


class RoleFilters(BaseModel):
    name: Optional[str] = None
    is_system_role: Optional[bool] = None
    hierarchy_level: Optional[int] = Field(None, ge=0, le=10)
    parent_role_id: Optional[UUID] = None
    is_active: Optional[bool] = None
    page: int = Field(default=1, ge=1)
    page_size: int = Field(default=10, ge=1, le=100)


class RoleHierarchy(BaseModel):
    """Model for role hierarchy representation"""
    id: UUID
    name: str
    description: Optional[str]
    hierarchy_level: int
    parent_role_id: Optional[UUID]
    children: List['RoleHierarchy'] = []
    is_system_role: bool
    is_active: bool

    class Config:
        from_attributes = True


class RoleAssignment(BaseModel):
    """Model for role assignment to users"""
    user_id: UUID
    role_id: UUID
    assigned_by: str = Field(..., min_length=1, max_length=255)
    assigned_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class RolePermissionAssignment(BaseModel):
    """Model for permission assignment to roles"""
    role_id: UUID
    permission_id: UUID
    granted_by: str = Field(..., min_length=1, max_length=255)
    granted_at: Optional[datetime] = None

    class Config:
        from_attributes = True


# Additional validation functions
def validate_role_name_format(name: str) -> bool:
    """
    Validate role name format.
    
    Args:
        name: Role name to validate
        
    Returns:
        bool: True if name format is valid
    """
    if not name or len(name.strip()) == 0:
        return False
    if len(name.strip()) > 100:
        return False
    # Allow letters, numbers, underscores, hyphens
    return re.match(r'^[a-zA-Z0-9_-]+$', name.strip()) is not None


def validate_role_hierarchy(role_level: int, parent_level: Optional[int] = None) -> bool:
    """
    Validate role hierarchy levels.
    
    Args:
        role_level: Current role hierarchy level
        parent_level: Parent role hierarchy level (if any)
        
    Returns:
        bool: True if hierarchy is valid
    """
    if role_level < 0 or role_level > 10:
        return False
    
    if parent_level is not None:
        # Child role should have higher level number than parent
        if role_level <= parent_level:
            return False
    
    return True


def sanitize_role_input(value: str) -> str:
    """
    Sanitize role input by removing potentially harmful characters.
    
    Args:
        value: Input value to sanitize
        
    Returns:
        str: Sanitized value
    """
    if not value:
        return value
    
    # Remove potential SQL injection characters and XSS attempts
    dangerous_chars = ['<', '>', '"', "'", '&', ';', '--', '/*', '*/', 'script']
    sanitized = value
    
    for char in dangerous_chars:
        sanitized = sanitized.replace(char, '')
    
    return sanitized.strip()


class RoleValidationError(Exception):
    """Custom exception for role validation errors"""
    pass


def validate_role_data_integrity(role_data: dict) -> tuple[bool, List[str]]:
    """
    Comprehensive validation of role data integrity.
    
    Args:
        role_data: Dictionary containing role data
        
    Returns:
        tuple: (is_valid, list_of_errors)
    """
    errors = []
    
    # Check required fields
    if 'name' not in role_data or not role_data['name']:
        errors.append("Role name is required")
    elif not validate_role_name_format(role_data['name']):
        errors.append("Invalid role name format")
    
    # Check hierarchy level
    if 'hierarchy_level' in role_data:
        try:
            level = int(role_data['hierarchy_level'])
            if level < 0 or level > 10:
                errors.append("Hierarchy level must be between 0 and 10")
        except (ValueError, TypeError):
            errors.append("Invalid hierarchy level format")
    
    # Check parent role ID if present
    if 'parent_role_id' in role_data and role_data['parent_role_id']:
        try:
            UUID(str(role_data['parent_role_id']))
        except (ValueError, TypeError):
            errors.append("Invalid parent_role_id format")
    
    # Check system role constraints
    if role_data.get('is_system_role', False):
        valid_system_roles = ['system_admin', 'project_admin', 'recruiter', 'interviewer', 'read_user']
        if role_data.get('name') not in valid_system_roles:
            errors.append(f"System role name must be one of: {', '.join(valid_system_roles)}")
    
    return len(errors) == 0, errors


# Update RoleHierarchy to handle forward references
RoleHierarchy.model_rebuild()
