# user_controller.py
"""
Controller layer for user management operations.
Handles HTTP request processing, validation, and response formatting.
"""

import logging
from typing import Optional
from uuid import UUID

from fastapi import HTTPException

from models.user import (
    User,
    UserCreate,
    UserUpdate,
    UserResponse,
    UserFilters,
    UserListResponse
)
from services.user_service import (
    create_user,
    get_user_by_id,
    get_user_by_email,
    update_user,
    delete_user,
    list_users,
    activate_user,
    assign_role_to_user,
    verify_user_exists_by_email
)

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


def create_user_controller(user_data: UserCreate) -> UserResponse:
    """
    Create a new user.

    Args:
        user_data: User creation data

    Returns:
        UserResponse: Created user data

    Raises:
        HTTPException: If user creation fails
    """
    try:
        logger.info(f"Creating user with email: {user_data.email}")
        result = create_user(user_data)
        logger.info(f"User created successfully: {result.id}")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error creating user: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def get_user_by_id_controller(user_id: UUID) -> UserResponse:
    """
    Retrieve a user by ID.

    Args:
        user_id: User UUID

    Returns:
        UserResponse: User data

    Raises:
        HTTPException: If user not found
    """
    try:
        logger.info(f"Retrieving user by ID: {user_id}")
        result = get_user_by_id(user_id)
        if not result:
            raise HTTPException(status_code=404, detail="User not found")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error retrieving user by ID: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def get_user_by_email_controller(email: str) -> UserResponse:
    """
    Retrieve a user by email address.

    Args:
        email: User email address

    Returns:
        UserResponse: User data

    Raises:
        HTTPException: If user not found
    """
    try:
        logger.info(f"Retrieving user by email: {email}")
        result = get_user_by_email(email)
        if not result:
            raise HTTPException(status_code=404, detail="User not found")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error retrieving user by email: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def update_user_controller(user_id: UUID, user_data: UserUpdate) -> UserResponse:
    """
    Update an existing user.

    Args:
        user_id: User UUID
        user_data: User update data

    Returns:
        UserResponse: Updated user data

    Raises:
        HTTPException: If user not found or update fails
    """
    try:
        logger.info(f"Updating user: {user_id}")
        result = update_user(user_id, user_data)
        logger.info(f"User updated successfully: {result.id}")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error updating user: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def delete_user_controller(user_id: UUID) -> dict:
    """
    Delete a user (soft delete).

    Args:
        user_id: User UUID

    Returns:
        dict: Success message

    Raises:
        HTTPException: If user not found or deletion fails
    """
    try:
        logger.info(f"Deleting user: {user_id}")
        success = delete_user(user_id)
        if success:
            logger.info(f"User deleted successfully: {user_id}")
            return {"message": "User deleted successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to delete user")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error deleting user: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def list_users_controller(filters: UserFilters) -> UserListResponse:
    """
    List users with filtering and pagination.

    Args:
        filters: User filtering and pagination parameters

    Returns:
        UserListResponse: Paginated list of users
    """
    try:
        logger.info(f"Listing users with filters: page={filters.page}, page_size={filters.page_size}")
        result = list_users(filters)
        logger.info(f"Retrieved {len(result.users)} users out of {result.total} total")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error listing users: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def activate_user_controller(user_id: UUID, activated_by: str) -> UserResponse:
    """
    Activate a user account.

    Args:
        user_id: User UUID
        activated_by: Email of user performing the activation

    Returns:
        UserResponse: Updated user data

    Raises:
        HTTPException: If user not found or activation fails
    """
    try:
        logger.info(f"Activating user: {user_id} by {activated_by}")
        result = activate_user(user_id, activated_by)
        logger.info(f"User activated successfully: {result.id}")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error activating user: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def assign_role_to_user_controller(user_id: UUID, role_id: UUID, assigned_by: str) -> UserResponse:
    """
    Assign a role to a user.

    Args:
        user_id: User UUID
        role_id: Role UUID
        assigned_by: Email of user performing the assignment

    Returns:
        UserResponse: Updated user data

    Raises:
        HTTPException: If user not found or assignment fails
    """
    try:
        logger.info(f"Assigning role {role_id} to user {user_id} by {assigned_by}")
        result = assign_role_to_user(user_id, role_id, assigned_by)
        logger.info(f"Role assigned successfully to user: {result.id}")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error assigning role to user: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def verify_user_exists_controller(email: str) -> dict:
    """
    Verify if a user exists by email (for MSAL integration).

    Args:
        email: User email address

    Returns:
        dict: Verification result

    Raises:
        HTTPException: If verification fails
    """
    try:
        logger.info(f"Verifying user existence: {email}")
        exists = verify_user_exists_by_email(email)
        return {"email": email, "exists": exists, "active": exists}
    except Exception as e:
        logger.error(f"Unexpected error verifying user existence: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def get_user_profile_controller(email: str) -> UserResponse:
    """
    Get user profile for authenticated user (for JWT token generation).

    Args:
        email: User email address from MSAL token

    Returns:
        UserResponse: User profile data

    Raises:
        HTTPException: If user not found or not active
    """
    try:
        logger.info(f"Getting user profile: {email}")
        result = get_user_by_email(email)
        if not result:
            raise HTTPException(status_code=404, detail="User not found")
        if not result.is_active:
            raise HTTPException(status_code=403, detail="User account is inactive")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error getting user profile: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
