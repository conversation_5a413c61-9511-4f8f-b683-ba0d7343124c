# positions_profile_service.py
"""
Service layer for positions_profile CRUD operations.
Handles business logic and database interactions for profile positions.
"""

from contextlib import contextmanager
from typing import List, Optional
from datetime import datetime
import logging

import psycopg2
from psycopg2 import sql
from psycopg2.extras import <PERSON><PERSON>, RealDictCursor
from fastapi import HTTPException

from core.config import settings
from models.positions_profile import (
    ProfilePosition,
    ProfilePositionCreate,
    ProfilePositionUpdate,
    ProfilePositionSearch,
    ProfilePositionResponse
)

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


# Database connection context manager
@contextmanager
def get_cursor():
    """
    Context manager for PostgreSQL cursor with error handling.

    Yields:
        cursor: PostgreSQL cursor with RealDictCursor factory

    Raises:
        HTTPException: If database connection fails
    """
    conn = None
    try:
        conn = psycopg2.connect(
            settings.DATABASE_URL,
            connect_timeout=120,
            options="-c statement_timeout=120000",
            keepalives_idle=30
        )
        with conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                yield cur
    except psycopg2.Error as e:
        logger.error(f"Database error occurred in get_cursor: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Database error: {str(e)}")
    finally:
        if conn:
            conn.close()


def create_profile_position(profile_data: ProfilePositionCreate) -> ProfilePosition:
    """
    Create a new profile position in the database.

    Args:
        profile_data (ProfilePositionCreate): Profile position data to create

    Returns:
        ProfilePosition: The created profile position

    Raises:
        HTTPException: If creation fails
    """
    try:
        with get_cursor() as cur:
            cur.execute(
                """
                INSERT INTO positions_profile (
                    profile_info,
                    created_by,
                    created_at,
                    updated_at
                )
                VALUES (%s, %s, NOW(), NOW())
                RETURNING id, profile_info, reason, is_active, is_deleted,
                          created_at, created_by, updated_at, updated_by
                """,
                (
                    Json(profile_data.profile_info),
                    profile_data.created_by
                )
            )
            row = cur.fetchone()

            if not row:
                raise HTTPException(
                    status_code=500, detail="Failed to create profile position")

            return ProfilePosition(
                id=str(row['id']),
                profile_info=row['profile_info'],
                reason=row['reason'],
                is_active=row['is_active'],
                is_deleted=row['is_deleted'],
                created_at=row['created_at'],
                created_by=row['created_by'],
                updated_at=row['updated_at'],
                updated_by=row['updated_by']
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating profile position: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error creating profile position: {str(e)}")


def get_profile_position_by_id(profile_id: str) -> Optional[ProfilePosition]:
    """
    Retrieve a profile position by its ID.

    Args:
        profile_id (str): The UUID of the profile position

    Returns:
        Optional[ProfilePosition]: The profile position if found, None otherwise

    Raises:
        HTTPException: If database query fails
    """
    try:
        with get_cursor() as cur:
            cur.execute(
                """
                SELECT id, profile_info, reason, is_active, is_deleted,
                       created_at, created_by, updated_at, updated_by
                FROM positions_profile
                WHERE id = %s AND is_deleted = false
                """,
                (profile_id,)
            )
            row = cur.fetchone()

            if not row:
                return None

            return ProfilePosition(
                id=str(row['id']),
                profile_info=row['profile_info'],
                reason=row['reason'],
                is_active=row['is_active'],
                is_deleted=row['is_deleted'],
                created_at=row['created_at'],
                created_by=row['created_by'],
                updated_at=row['updated_at'],
                updated_by=row['updated_by']
            )
    except Exception as e:
        logger.error(f"Error fetching profile position by ID: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error fetching profile position: {str(e)}")


def update_profile_position(profile_data: ProfilePositionUpdate) -> ProfilePosition:
    """
    Update an existing profile position in the database.

    Args:
        profile_data (ProfilePositionUpdate): Profile position data to update

    Returns:
        ProfilePosition: The updated profile position

    Raises:
        HTTPException: If update fails or profile not found
    """
    try:
        with get_cursor() as cur:
            cur.execute(
                """
                UPDATE positions_profile
                SET profile_info = %s,
                    updated_by = %s,
                    updated_at = NOW()
                WHERE id = %s AND is_deleted = false
                RETURNING id, profile_info, reason, is_active, is_deleted,
                          created_at, created_by, updated_at, updated_by
                """,
                (
                    Json(profile_data.profile_info),
                    profile_data.updated_by,
                    profile_data.id
                )
            )
            row = cur.fetchone()

            if not row:
                raise HTTPException(
                    status_code=404,
                    detail=f"Profile position with ID {profile_data.id} not found"
                )

            return ProfilePosition(
                id=str(row['id']),
                profile_info=row['profile_info'],
                reason=row['reason'],
                is_active=row['is_active'],
                is_deleted=row['is_deleted'],
                created_at=row['created_at'],
                created_by=row['created_by'],
                updated_at=row['updated_at'],
                updated_by=row['updated_by']
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating profile position: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error updating profile position: {str(e)}")


def delete_profile_position(profile_id: str, deleted_by: Optional[str] = None) -> bool:
    """
    Soft delete a profile position (sets is_deleted to true).

    Args:
        profile_id (str): The UUID of the profile position to delete
        deleted_by (Optional[str]): User who is deleting the profile

    Returns:
        bool: True if deletion was successful

    Raises:
        HTTPException: If deletion fails or profile not found
    """
    try:
        with get_cursor() as cur:
            cur.execute(
                """
                UPDATE positions_profile
                SET is_deleted = true,
                    is_active = false,
                    updated_by = %s,
                    updated_at = NOW()
                WHERE id = %s AND is_deleted = false
                RETURNING id
                """,
                (deleted_by, profile_id)
            )
            row = cur.fetchone()

            if not row:
                raise HTTPException(
                    status_code=404,
                    detail=f"Profile position with ID {profile_id} not found"
                )

            return True
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting profile position: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error deleting profile position: {str(e)}")


def get_all_profile_positions(
    page: int = 1,
    page_size: int = 10,
    filters: Optional[ProfilePositionSearch] = None
) -> ProfilePositionResponse:
    """
    Retrieve all profile positions with pagination and optional filtering.

    Args:
        page (int): Page number (1-indexed)
        page_size (int): Number of items per page
        filters (Optional[ProfilePositionSearch]): Optional search filters

    Returns:
        ProfilePositionResponse: Paginated list of profile positions with total count

    Raises:
        HTTPException: If query fails
    """
    try:
        offset = (page - 1) * page_size

        # Build WHERE clause based on filters
        where_conditions = ["is_deleted = false"]
        params = []

        if filters:
            if filters.profile_id:
                where_conditions.append("id = %s")
                params.append(filters.profile_id)

            if filters.is_active is not None:
                where_conditions.append("is_active = %s")
                params.append(filters.is_active)

            if filters.created_by:
                where_conditions.append("created_by = %s")
                params.append(filters.created_by)

        where_clause = " AND ".join(where_conditions)

        # Get total count
        with get_cursor() as cur:
            count_query = f"SELECT COUNT(*) FROM positions_profile WHERE {where_clause}"
            cur.execute(count_query, params)
            total_items = cur.fetchone()['count']

        # Get paginated results
        with get_cursor() as cur:
            query = f"""
                SELECT id, profile_info, reason, is_active, is_deleted,
                       created_at, created_by, updated_at, updated_by
                FROM positions_profile
                WHERE {where_clause}
                ORDER BY created_at DESC
                LIMIT %s OFFSET %s
            """
            params.extend([page_size, offset])
            cur.execute(query, params)
            rows = cur.fetchall()

        items = [
            ProfilePosition(
                id=str(row['id']),
                profile_info=row['profile_info'],
                reason=row['reason'],
                is_active=row['is_active'],
                is_deleted=row['is_deleted'],
                created_at=row['created_at'],
                created_by=row['created_by'],
                updated_at=row['updated_at'],
                updated_by=row['updated_by']
            )
            for row in rows
        ]

        return ProfilePositionResponse(
            total_items=total_items,
            items=items
        )
    except Exception as e:
        logger.error(f"Error fetching profile positions: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error fetching profile positions: {str(e)}")
