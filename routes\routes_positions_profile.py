# routes_positions_profile.py
"""
FastAPI routes for positions_profile CRUD operations.
Defines all HTTP endpoints for profile position management.
"""

from typing import Optional
import logging

from fastapi import APIRouter, HTTPException, Query, Path, Body
from opentelemetry import trace

from models.positions_profile import (
    ProfilePosition,
    ProfilePositionCreate,
    ProfilePositionUpdate,
    ProfilePositionSearch,
    ProfilePositionResponse
)
from controllers.positions_profile_controller import (
    create_profile_position,
    get_profile_position_by_id,
    update_profile_position,
    delete_profile_position,
    get_all_profile_positions
)

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

# OpenTelemetry tracer
tracer = trace.get_tracer(__name__)

# Create router
router = APIRouter()


###################################################
# Profile Position CRUD Endpoints
###################################################


@router.post("/", response_model=ProfilePosition, status_code=201)
def create_profile_position_endpoint(
    profile_data: ProfilePositionCreate = Body(
        ...,
        description="Profile position data to create"
    )
):
    """
    Create a new profile position.

    **Request Body:**
    - `profile_info` (dict): Information about the profile (required)
    - `created_by` (str): User who is creating the profile (optional)

    **Returns:**
    - The created profile position with all fields including generated ID

    **Raises:**
    - 500: If creation fails
    """
    try:
        logger.info("POST /positions_profile - Creating new profile position")
        result = create_profile_position(profile_data)
        logger.info(
            f"Successfully created profile position with ID: {result.id}")
        return result
    except HTTPException as http_exc:
        logger.error(
            f"HTTP Exception while creating profile position: {http_exc.detail}")
        raise http_exc
    except Exception as e:
        logger.error(f"Error creating profile position: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error occurred while creating profile position: {str(e)}"
        )


@router.get("/{profile_id}", response_model=ProfilePosition)
def get_profile_position_endpoint(
    profile_id: str = Path(
        ...,
        description="UUID of the profile position to retrieve"
    )
):
    """
    Retrieve a profile position by its ID.

    **Path Parameters:**
    - `profile_id` (str): UUID of the profile position

    **Returns:**
    - The requested profile position

    **Raises:**
    - 404: If profile position not found
    - 500: If query fails
    """
    try:
        logger.info(
            f"GET /positions_profile/{profile_id} - Fetching profile position")
        result = get_profile_position_by_id(profile_id)
        logger.info(
            f"Successfully retrieved profile position with ID: {profile_id}")
        return result
    except HTTPException as http_exc:
        logger.error(
            f"HTTP Exception while fetching profile position: {http_exc.detail}")
        raise http_exc
    except Exception as e:
        logger.error(f"Error fetching profile position: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error occurred while fetching profile position: {str(e)}"
        )


@router.put("/{profile_id}", response_model=ProfilePosition)
def update_profile_position_endpoint(
    profile_id: str = Path(
        ...,
        description="UUID of the profile position to update"
    ),
    profile_data: ProfilePositionUpdate = Body(
        ...,
        description="Updated profile position data"
    )
):
    """
    Update an existing profile position.

    **Path Parameters:**
    - `profile_id` (str): UUID of the profile position to update

    **Request Body:**
    - `id` (str): UUID of the profile position (must match path parameter)
    - `profile_info` (dict): Updated profile information
    - `updated_by` (str): User who is updating the profile (optional)

    **Returns:**
    - The updated profile position

    **Raises:**
    - 400: If profile_id in path doesn't match id in body
    - 404: If profile position not found
    - 500: If update fails
    """
    try:
        # Validate that path parameter matches body id
        if profile_data.id != profile_id:
            raise HTTPException(
                status_code=400,
                detail=f"Profile ID in path ({profile_id}) does not match ID in body ({profile_data.id})"
            )

        logger.info(
            f"PUT /positions_profile/{profile_id} - Updating profile position")
        result = update_profile_position(profile_data)
        logger.info(
            f"Successfully updated profile position with ID: {profile_id}")
        return result
    except HTTPException as http_exc:
        logger.error(
            f"HTTP Exception while updating profile position: {http_exc.detail}")
        raise http_exc
    except Exception as e:
        logger.error(f"Error updating profile position: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error occurred while updating profile position: {str(e)}"
        )


@router.delete("/{profile_id}")
def delete_profile_position_endpoint(
    profile_id: str = Path(
        ...,
        description="UUID of the profile position to delete"
    ),
    deleted_by: Optional[str] = Query(
        None,
        description="User who is deleting the profile"
    )
):
    """
    Delete a profile position (soft delete).

    **Path Parameters:**
    - `profile_id` (str): UUID of the profile position to delete

    **Query Parameters:**
    - `deleted_by` (str): User who is deleting the profile (optional)

    **Returns:**
    - Success message with deletion details

    **Raises:**
    - 404: If profile position not found
    - 500: If deletion fails
    """
    try:
        logger.info(
            f"DELETE /positions_profile/{profile_id} - Deleting profile position")
        result = delete_profile_position(profile_id, deleted_by)
        logger.info(
            f"Successfully deleted profile position with ID: {profile_id}")
        return result
    except HTTPException as http_exc:
        logger.error(
            f"HTTP Exception while deleting profile position: {http_exc.detail}")
        raise http_exc
    except Exception as e:
        logger.error(f"Error deleting profile position: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error occurred while deleting profile position: {str(e)}"
        )


@router.get("/", response_model=ProfilePositionResponse)
def get_all_profile_positions_endpoint(
    page: int = Query(
        1,
        ge=1,
        description="Page number (1-indexed)"
    ),
    page_size: int = Query(
        10,
        ge=1,
        le=100,
        description="Number of items per page (max 100)"
    ),
    profile_id: Optional[str] = Query(
        None,
        description="Filter by profile ID"
    ),
    is_active: Optional[bool] = Query(
        None,
        description="Filter by active status"
    ),
    created_by: Optional[str] = Query(
        None,
        description="Filter by creator"
    )
):
    """
    Retrieve all profile positions with pagination and optional filtering.

    **Query Parameters:**
    - `page` (int): Page number, starting from 1 (default: 1)
    - `page_size` (int): Number of items per page, max 100 (default: 10)
    - `profile_id` (str): Filter by specific profile ID (optional)
    - `is_active` (bool): Filter by active status (optional)
    - `created_by` (str): Filter by creator username (optional)

    **Returns:**
    - Paginated list of profile positions with total count

    **Raises:**
    - 500: If query fails
    """
    try:
        logger.info(
            f"GET /positions_profile - Fetching profile positions (page={page}, size={page_size})")

        # Build filters if any are provided
        filters = None
        if profile_id or is_active is not None or created_by:
            filters = ProfilePositionSearch(
                profile_id=profile_id,
                is_active=is_active,
                created_by=created_by
            )

        result = get_all_profile_positions(page, page_size, filters)
        logger.info(
            f"Successfully retrieved {len(result.items)} profile positions")
        return result
    except HTTPException as http_exc:
        logger.error(
            f"HTTP Exception while fetching profile positions: {http_exc.detail}")
        raise http_exc
    except Exception as e:
        logger.error(f"Error fetching profile positions: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error occurred while fetching profile positions: {str(e)}"
        )
