# Git files
.git
.gitignore
.gitattributes

# Python cache and compiled files
__pycache__
*.py[cod]
*$py.class
*.so
.Python

# Virtual environments
venv/
env/
ENV/
.venv

# Testing
.pytest_cache/
.coverage
htmlcov/
*.cover
.hypothesis/
test_*.py
*_test.py
tests/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store

# Documentation
*.md
README.md
CHANGELOG.md
docs/

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml
azure-pipelines.yml

# Docker files (don't copy into image)
Dockerfile*
docker-compose*.yml
.dockerignore

# Logs
*.log
logs/

# Environment files (should be provided at runtime)
.env
.env.*
!.env.example

# Temporary files
tmp/
temp/
*.tmp

# Build artifacts
build/
dist/
*.egg-info/

# Database files
*.db
*.sqlite
*.sqlite3

# OS files
Thumbs.db
.DS_Store

# Backup files
*.bak
*.backup

# Large data files
*.csv
*.xlsx
*.xls
data/

# Test output files
*.docx
*.pdf
test_output/
output/

# Jupyter notebooks
*.ipynb
.ipynb_checkpoints/

# Node modules (if any)
node_modules/

# Package manager files
package-lock.json
yarn.lock
