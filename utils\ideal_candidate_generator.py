#!/usr/bin/env python3
"""
Ideal Candidate Generator for SmartHR Position Matching.

This module generates comprehensive ideal candidate profiles based on position 
descriptions. The generated candidates follow the full Lumus markdown format 
and can be processed with the chunked embedding system for enhanced matching.

Key Features:
- Generates realistic ideal candidate profiles from position descriptions
- Follows complete Lumus JSON structure with all sections
- Creates appropriate work experience, education, and skills
- Generates realistic projects and certifications
- Supports multiple seniority levels (Junior, Mid, Senior)
- Compatible with chunked embedding processing
"""

import logging
import re
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import random

logger = logging.getLogger(__name__)


class IdealCandidateGenerator:
    """
    Generator for creating ideal candidate profiles from position descriptions.
    
    This class analyzes position requirements and generates comprehensive
    candidate profiles that would be perfect matches for the position.
    """
    
    def __init__(self):
        """Initialize the ideal candidate generator."""
        self.skill_categories = {
            'programming_languages': ['Python', 'Java', 'C++', 'JavaScript', 'TypeScript', 'Go', 'Rust', 'C#'],
            'ai_ml_frameworks': ['TensorFlow', 'PyTorch', 'Keras', 'Scikit-learn', 'Hugging Face', '<PERSON><PERSON>hain', 'LangGraph'],
            'cloud_platforms': ['AWS', 'Azure', 'GCP', 'Docker', 'Kubernetes'],
            'databases': ['PostgreSQL', 'MongoDB', 'Redis', 'Elasticsearch', 'Vector Databases'],
            'tools': ['Git', 'CI/CD', 'MLOps', 'Power Apps', 'n8n', 'Jupyter', 'VS Code']
        }
        
        self.seniority_experience_mapping = {
            'Junior': {'min_years': 1, 'max_years': 3, 'positions': 1},
            'Mid': {'min_years': 3, 'max_years': 6, 'positions': 2},
            'Senior': {'min_years': 6, 'max_years': 10, 'positions': 3},
            'Lead': {'min_years': 8, 'max_years': 12, 'positions': 3},
            'Principal': {'min_years': 10, 'max_years': 15, 'positions': 4}
        }
    
    def generate_ideal_candidate(self, position_description: str) -> Dict[str, Any]:
        """
        Generate an ideal candidate profile from a position description.
        
        Args:
            position_description (str): Complete position description text
            
        Returns:
            Dict[str, Any]: Complete candidate profile in Lumus JSON format
        """
        try:
            logger.info("Generating ideal candidate from position description")
            
            # Parse position requirements
            requirements = self._parse_position_requirements(position_description)
            
            # Generate candidate profile
            candidate_profile = self._generate_candidate_profile(requirements)
            
            logger.info(f"Generated ideal candidate for {requirements.get('position_name', 'Unknown Position')}")
            return candidate_profile
            
        except Exception as e:
            logger.error(f"Error generating ideal candidate: {str(e)}")
            raise
    
    def _parse_position_requirements(self, position_text: str) -> Dict[str, Any]:
        """Parse position description to extract requirements."""
        requirements = {
            'position_name': self._extract_position_name(position_text),
            'seniority_level': self._extract_seniority_level(position_text),
            'client': self._extract_client(position_text),
            'project': self._extract_project(position_text),
            'location': self._extract_location(position_text),
            'required_skills': self._extract_required_skills(position_text),
            'nice_to_have_skills': self._extract_nice_to_have_skills(position_text),
            'soft_skills': self._extract_soft_skills(position_text),
            'languages': self._extract_languages(position_text),
            'responsibilities': self._extract_responsibilities(position_text),
            'job_description': self._extract_job_description(position_text)
        }
        
        return requirements
    
    def _extract_position_name(self, text: str) -> str:
        """Extract position name from text."""
        match = re.search(r'\*\*Position Name:\*\*\s*([^\n]+)', text)
        if match:
            return match.group(1).strip()
        
        # Fallback patterns
        patterns = [
            r'seeking an?\s+([^.]+?)(?:\s+with|\.)',
            r'Position:\s*([^\n]+)',
            r'Role:\s*([^\n]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1).strip()
        
        return "Software Engineer"
    
    def _extract_seniority_level(self, text: str) -> str:
        """Extract seniority level from text."""
        match = re.search(r'\*\*Seniority Level:\*\*\s*([^\n]+)', text)
        if match:
            return match.group(1).strip()
        
        # Look for seniority indicators in text
        if re.search(r'\b(senior|lead|principal)\b', text, re.IGNORECASE):
            return "Senior"
        elif re.search(r'\b(junior|entry|graduate)\b', text, re.IGNORECASE):
            return "Junior"
        else:
            return "Mid"
    
    def _extract_client(self, text: str) -> str:
        """Extract client from text."""
        match = re.search(r'\*\*Client:\*\*\s*([^\n]+)', text)
        return match.group(1).strip() if match else "Technology Company"
    
    def _extract_project(self, text: str) -> str:
        """Extract project from text."""
        match = re.search(r'\*\*Project:\*\*\s*([^\n]+)', text)
        return match.group(1).strip() if match else "AI/ML Initiative"
    
    def _extract_location(self, text: str) -> str:
        """Extract location from text."""
        patterns = [
            r'\*\*Position Type:\*\*\s*([^\n]+)',
            r'\*\*Position Allocations:\*\*\s*([^\n]+)',
            r'\*\*Location:\*\*\s*([^\n]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                location = match.group(1).strip()
                if 'remote' in location.lower():
                    return "Remote"
                elif 'latam' in location.lower():
                    return "Latin America"
                else:
                    return location
        
        return "Remote"
    
    def _extract_required_skills(self, text: str) -> List[Dict[str, Any]]:
        """Extract required skills from text."""
        skills = []
        
        # Look for Professional Skills section
        prof_skills_match = re.search(r'\*\*Professional Skills:\*\*\s*\n(.*?)(?=\n\s*\*\*|\n\s*-\s*\*\*|$)', text, re.DOTALL)
        if prof_skills_match:
            skills_text = prof_skills_match.group(1)
            skill_matches = re.findall(r'-\s*([^(]+)\s*\(([^)]+)\)', skills_text)
            
            for skill_name, proficiency in skill_matches:
                skills.append({
                    'name': skill_name.strip(),
                    'proficiency_level': proficiency.strip(),
                    'years_of_experience': self._proficiency_to_years(proficiency.strip()),
                    'category': 'required'
                })
        
        return skills
    
    def _extract_nice_to_have_skills(self, text: str) -> List[Dict[str, Any]]:
        """Extract nice-to-have skills from text."""
        skills = []
        
        # Look for Nice to Have section
        nice_to_have_match = re.search(r'\*\*Nice to Have:\*\*\s*\n(.*?)(?=\n\s*\*\*|\n\s*-\s*\*\*|$)', text, re.DOTALL)
        if nice_to_have_match:
            skills_text = nice_to_have_match.group(1)
            skill_matches = re.findall(r'-\s*([^(]+)\s*\(([^)]+)\)', skills_text)
            
            for skill_name, proficiency in skill_matches:
                skills.append({
                    'name': skill_name.strip(),
                    'proficiency_level': proficiency.strip(),
                    'years_of_experience': self._proficiency_to_years(proficiency.strip()),
                    'category': 'nice_to_have'
                })
        
        return skills
    
    def _extract_soft_skills(self, text: str) -> List[Dict[str, Any]]:
        """Extract soft skills from text."""
        skills = []
        
        # Look for Soft Skills section
        soft_skills_match = re.search(r'\*\*Soft Skills:\*\*\s*\n(.*?)(?=\n\s*\*\*|\n\s*-\s*\*\*|$)', text, re.DOTALL)
        if soft_skills_match:
            skills_text = soft_skills_match.group(1)
            skill_matches = re.findall(r'-\s*([^(]+)\s*\(([^)]+)\)', skills_text)
            
            for skill_name, proficiency in skill_matches:
                skills.append({
                    'name': skill_name.strip(),
                    'proficiency_level': proficiency.strip()
                })
        
        return skills
    
    def _extract_languages(self, text: str) -> List[Dict[str, str]]:
        """Extract language requirements from text."""
        languages = []
        
        # Look for Languages section
        lang_match = re.search(r'\*\*Languages:\*\*\s*\n(.*?)(?=\n\s*\*\*|$)', text, re.DOTALL)
        if lang_match:
            lang_text = lang_match.group(1)
            lang_matches = re.findall(r'-\s*([^(]+)\s*\(([^)]+)\)', lang_text)
            
            for lang_name, proficiency in lang_matches:
                languages.append({
                    'language': lang_name.strip(),
                    'proficiency_level': proficiency.strip()
                })
        
        # Default to English if no languages specified
        if not languages:
            languages.append({
                'language': 'English',
                'proficiency_level': 'Advanced'
            })
        
        return languages
    
    def _extract_responsibilities(self, text: str) -> List[str]:
        """Extract main responsibilities from text."""
        responsibilities = []
        
        # Look for Main Responsibilities section
        resp_match = re.search(r'\*\*Main Responsibilities:\*\*\s*\n(.*?)(?=\n\s*\*\*|$)', text, re.DOTALL)
        if resp_match:
            resp_text = resp_match.group(1)
            resp_lines = re.findall(r'-\s*([^\n]+)', resp_text)
            responsibilities.extend([resp.strip() for resp in resp_lines])
        
        return responsibilities
    
    def _extract_job_description(self, text: str) -> str:
        """Extract job description from text."""
        # Look for Job Description section
        desc_match = re.search(r'\*\*Job Description:\*\*\s*\n(.*?)(?=\n\s*\*\*|$)', text, re.DOTALL)
        if desc_match:
            return desc_match.group(1).strip()
        
        # Fallback to first paragraph
        lines = text.split('\n')
        for line in lines:
            if len(line.strip()) > 50 and not line.strip().startswith('**'):
                return line.strip()
        
        return "Seeking a qualified professional for this position."
    
    def _proficiency_to_years(self, proficiency: str) -> Optional[int]:
        """Convert proficiency level to years of experience."""
        proficiency_lower = proficiency.lower()
        
        if 'beginner' in proficiency_lower or 'basic' in proficiency_lower:
            return 1
        elif 'lower intermediate' in proficiency_lower:
            return 2
        elif 'intermediate' in proficiency_lower:
            return 3
        elif 'upper intermediate' in proficiency_lower:
            return 4
        elif 'advanced' in proficiency_lower:
            return 5
        elif 'expert' in proficiency_lower:
            return 7
        
        return None
    
    def _generate_candidate_profile(self, requirements: Dict[str, Any]) -> Dict[str, Any]:
        """Generate complete candidate profile based on requirements."""
        seniority = requirements.get('seniority_level', 'Mid')
        experience_config = self.seniority_experience_mapping.get(seniority, self.seniority_experience_mapping['Mid'])
        
        # Generate personal info
        personal_info = self._generate_personal_info(requirements)
        
        # Generate professional summary
        summary = self._generate_professional_summary(requirements, experience_config)
        
        # Generate education
        education = self._generate_education(requirements, seniority)
        
        # Generate work experience
        work_experience = self._generate_work_experience(requirements, experience_config)
        
        # Generate skills
        skills = self._generate_skills(requirements)
        
        # Generate soft skills
        soft_skills = self._generate_soft_skills_list(requirements)
        
        # Generate certifications
        certifications = self._generate_certifications(requirements)
        
        # Generate languages
        languages = requirements.get('languages', [])
        
        # Generate projects
        projects = self._generate_projects(requirements)
        
        # Generate roles
        roles = self._generate_roles(requirements)
        
        return {
            'personal_info': personal_info,
            'summary': summary,
            'education': education,
            'work_experience': work_experience,
            'skills': skills,
            'soft_skills': soft_skills,
            'certifications': certifications,
            'languages': languages,
            'projects': projects,
            'roles': roles,
            'references': None
        }
    
    def _generate_personal_info(self, requirements: Dict[str, Any]) -> Dict[str, Any]:
        """Generate personal information."""
        return {
            'full_name': 'Ideal Candidate',
            'email': '<EMAIL>',
            'phone_number': '+****************',
            'city': 'Remote' if requirements.get('location') == 'Remote' else 'Tech Hub City',
            'country': 'Global' if requirements.get('location') == 'Remote' else 'United States',
            'address': None,
            'linkedin_profile': 'https://linkedin.com/in/ideal-candidate',
            'website': 'https://ideal-candidate-portfolio.com'
        }

    def _generate_professional_summary(self, requirements: Dict[str, Any], experience_config: Dict[str, Any]) -> str:
        """Generate professional summary based on requirements."""
        position_name = requirements.get('position_name', 'Software Engineer')
        seniority = requirements.get('seniority_level', 'Mid')
        years = experience_config['max_years']

        # Get key skills for summary
        required_skills = requirements.get('required_skills', [])
        key_skills = [skill['name'] for skill in required_skills[:5]]  # Top 5 skills

        summary_parts = [
            f"Highly skilled {position_name} with {years}+ years of experience in software development and technology solutions.",
            f"Proven expertise in {', '.join(key_skills[:3])} with a strong track record of delivering high-quality projects.",
            "Experienced in full software development lifecycle, from requirements analysis to deployment and maintenance.",
            "Strong problem-solving abilities and excellent communication skills for collaborating with cross-functional teams."
        ]

        if 'AI' in position_name or 'Machine Learning' in position_name:
            summary_parts.insert(1, "Specialized in artificial intelligence and machine learning solutions with hands-on experience in model development, training, and deployment.")

        return " ".join(summary_parts)

    def _generate_education(self, requirements: Dict[str, Any], seniority: str) -> List[Dict[str, Any]]:
        """Generate education background."""
        education = []

        # Determine degree based on position
        position_name = requirements.get('position_name', '').lower()

        if 'ai' in position_name or 'machine learning' in position_name or 'data' in position_name:
            degree = "Master of Science"
            field = "Computer Science - Machine Learning"
            institution = "Stanford University"
        elif 'engineer' in position_name:
            degree = "Bachelor of Science"
            field = "Computer Science"
            institution = "MIT"
        else:
            degree = "Bachelor of Science"
            field = "Software Engineering"
            institution = "Carnegie Mellon University"

        # Calculate graduation year based on seniority
        current_year = datetime.now().year
        experience_years = self.seniority_experience_mapping.get(seniority, {'max_years': 5})['max_years']
        grad_year = current_year - experience_years

        education.append({
            'institution_name': institution,
            'degree': degree,
            'field_of_study': field,
            'start_date': str(grad_year - 4),
            'end_date': str(grad_year),
            'location': 'United States',
            'description': f"Graduated with honors. Relevant coursework: Algorithms, Data Structures, Machine Learning, Software Engineering, Database Systems. Capstone project focused on {field.lower()} applications."
        })

        # Add additional education for senior roles
        if seniority in ['Senior', 'Lead', 'Principal']:
            education.append({
                'institution_name': 'Coursera / edX',
                'degree': 'Professional Certificate',
                'field_of_study': 'Advanced Machine Learning Specialization',
                'start_date': str(grad_year + 2),
                'end_date': str(grad_year + 3),
                'location': 'Online',
                'description': 'Completed advanced specialization in deep learning, natural language processing, and computer vision. Hands-on projects with TensorFlow and PyTorch.'
            })

        return education

    def _generate_work_experience(self, requirements: Dict[str, Any], experience_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate work experience based on requirements."""
        work_experience = []
        position_name = requirements.get('position_name', 'Software Engineer')
        responsibilities = requirements.get('responsibilities', [])
        required_skills = requirements.get('required_skills', [])

        current_year = datetime.now().year
        total_years = experience_config['max_years']
        num_positions = experience_config['positions']

        # Generate positions from most recent to oldest
        years_per_position = total_years // num_positions
        current_start_year = current_year - years_per_position

        for i in range(num_positions):
            if i == 0:  # Current position
                job_title = position_name
                company = requirements.get('client', 'Leading Tech Company')
                start_date = f"{current_start_year}"
                end_date = "Present"
                location = requirements.get('location', 'Remote')

                # Use actual responsibilities from job description
                job_responsibilities = responsibilities[:6] if responsibilities else [
                    f"Lead development of {position_name.lower()} solutions using cutting-edge technologies",
                    "Collaborate with cross-functional teams to deliver high-quality software products",
                    "Mentor junior developers and contribute to technical decision-making",
                    "Implement best practices for code quality, testing, and deployment"
                ]

                # Add skills from this position
                position_skills = [
                    {'name': skill['name'], 'proficiency_level': skill['proficiency_level'], 'years_of_experience': skill.get('years_of_experience')}
                    for skill in required_skills[:8]
                ]

            else:  # Previous positions
                # Generate progressive job titles
                if i == 1:
                    job_title = f"Senior {position_name}" if "Senior" not in position_name else position_name.replace("Senior ", "")
                    company = "Innovation Corp"
                else:
                    job_title = f"Junior {position_name}" if "Junior" not in position_name else position_name.replace("Junior ", "")
                    company = "TechStart Solutions"

                end_year = current_start_year
                start_year = end_year - years_per_position
                start_date = str(start_year)
                end_date = str(end_year)
                location = "San Francisco, CA" if i == 1 else "Austin, TX"

                # Generate relevant responsibilities for previous roles
                job_responsibilities = [
                    f"Developed and maintained {position_name.lower()} applications and systems",
                    "Participated in agile development processes and sprint planning",
                    "Collaborated with product managers and designers on feature development",
                    "Contributed to code reviews and technical documentation"
                ]

                # Subset of skills for previous positions
                position_skills = [
                    {'name': skill['name'], 'proficiency_level': 'Intermediate', 'years_of_experience': 2}
                    for skill in required_skills[:4]
                ]

                current_start_year = start_year

            work_experience.append({
                'job_title': job_title,
                'company_name': company,
                'start_date': start_date,
                'end_date': end_date,
                'location': location,
                'responsibilities': job_responsibilities,
                'skills': position_skills
            })

        return work_experience

    def _generate_skills(self, requirements: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate comprehensive skills list."""
        skills = []

        # Add required skills
        required_skills = requirements.get('required_skills', [])
        for skill in required_skills:
            skills.append({
                'name': skill['name'],
                'proficiency_level': skill['proficiency_level'],
                'years_of_experience': skill.get('years_of_experience')
            })

        # Add nice-to-have skills
        nice_to_have = requirements.get('nice_to_have_skills', [])
        for skill in nice_to_have:
            skills.append({
                'name': skill['name'],
                'proficiency_level': skill['proficiency_level'],
                'years_of_experience': skill.get('years_of_experience')
            })

        return skills

    def _generate_soft_skills_list(self, requirements: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate soft skills list."""
        soft_skills_from_req = requirements.get('soft_skills', [])

        # Default soft skills if none specified
        if not soft_skills_from_req:
            soft_skills_from_req = [
                {'name': 'Communication', 'proficiency_level': 'Advanced'},
                {'name': 'Team Work', 'proficiency_level': 'Advanced'},
                {'name': 'Problem Solving', 'proficiency_level': 'Advanced'},
                {'name': 'Leadership', 'proficiency_level': 'Intermediate'}
            ]

        return [
            {
                'name': skill['name'],
                'proficiency_level': skill['proficiency_level'],
                'description': f"Demonstrated {skill['name'].lower()} skills through successful project delivery and team collaboration"
            }
            for skill in soft_skills_from_req
        ]

    def _generate_certifications(self, requirements: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate relevant certifications."""
        certifications = []
        position_name = requirements.get('position_name', '').lower()
        required_skills = requirements.get('required_skills', [])

        # AI/ML related certifications
        if 'ai' in position_name or 'machine learning' in position_name:
            certifications.extend([
                {
                    'name': 'TensorFlow Developer Certificate',
                    'issuing_organization': 'Google',
                    'issue_date': '2023',
                    'expiration_date': None,
                    'credential_id': 'TF-2023-001',
                    'credential_url': 'https://www.credential.net/tensorflow-cert'
                },
                {
                    'name': 'AWS Certified Machine Learning - Specialty',
                    'issuing_organization': 'Amazon Web Services',
                    'issue_date': '2023',
                    'expiration_date': '2026',
                    'credential_id': 'AWS-ML-2023',
                    'credential_url': 'https://aws.amazon.com/certification/certified-machine-learning-specialty/'
                }
            ])

        # Cloud certifications based on skills
        cloud_skills = [skill['name'].lower() for skill in required_skills]
        if any('aws' in skill for skill in cloud_skills):
            certifications.append({
                'name': 'AWS Certified Solutions Architect',
                'issuing_organization': 'Amazon Web Services',
                'issue_date': '2022',
                'expiration_date': '2025',
                'credential_id': 'AWS-SA-2022',
                'credential_url': 'https://aws.amazon.com/certification/certified-solutions-architect-associate/'
            })

        if any('azure' in skill for skill in cloud_skills):
            certifications.append({
                'name': 'Microsoft Azure AI Engineer Associate',
                'issuing_organization': 'Microsoft',
                'issue_date': '2023',
                'expiration_date': '2025',
                'credential_id': 'MS-AI-2023',
                'credential_url': 'https://docs.microsoft.com/en-us/learn/certifications/azure-ai-engineer'
            })

        return certifications

    def _generate_projects(self, requirements: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate relevant projects."""
        projects = []
        position_name = requirements.get('position_name', '')
        required_skills = requirements.get('required_skills', [])

        # Get key technologies
        tech_skills = [skill['name'] for skill in required_skills if skill['name'] in
                      ['Python', 'TensorFlow', 'PyTorch', 'LangChain', 'AWS', 'Docker', 'React', 'Node.js']]

        if 'AI' in position_name or 'Machine Learning' in position_name:
            projects.extend([
                {
                    'name': 'Intelligent Document Processing System',
                    'description': 'Developed an end-to-end document processing system using NLP and computer vision to extract and classify information from various document types. Achieved 95% accuracy in document classification and 90% accuracy in information extraction.',
                    'role': 'Lead AI Engineer',
                    'technologies_used': tech_skills[:4] + ['OpenCV', 'spaCy'],
                    'start_date': '2023-01',
                    'end_date': '2023-06',
                    'url': 'https://github.com/ideal-candidate/document-processing'
                },
                {
                    'name': 'Conversational AI Chatbot Platform',
                    'description': 'Built a scalable chatbot platform using large language models and RAG architecture. Integrated with multiple data sources and deployed on cloud infrastructure serving 10,000+ daily users.',
                    'role': 'Senior Developer',
                    'technologies_used': ['LangChain', 'OpenAI API', 'Vector Databases', 'FastAPI'],
                    'start_date': '2022-08',
                    'end_date': '2023-01',
                    'url': 'https://github.com/ideal-candidate/chatbot-platform'
                }
            ])
        else:
            projects.extend([
                {
                    'name': 'Microservices Architecture Migration',
                    'description': 'Led the migration of a monolithic application to microservices architecture, improving system scalability and reducing deployment time by 60%. Implemented CI/CD pipelines and monitoring solutions.',
                    'role': 'Technical Lead',
                    'technologies_used': tech_skills[:3] + ['Kubernetes', 'Jenkins'],
                    'start_date': '2022-03',
                    'end_date': '2022-12',
                    'url': 'https://github.com/ideal-candidate/microservices-migration'
                },
                {
                    'name': 'Real-time Analytics Dashboard',
                    'description': 'Developed a real-time analytics dashboard for business intelligence, processing millions of events per day. Implemented data pipelines and visualization components for executive reporting.',
                    'role': 'Full Stack Developer',
                    'technologies_used': ['React', 'Node.js', 'PostgreSQL', 'Redis'],
                    'start_date': '2021-09',
                    'end_date': '2022-02',
                    'url': 'https://github.com/ideal-candidate/analytics-dashboard'
                }
            ])

        return projects

    def _generate_roles(self, requirements: Dict[str, Any]) -> List[str]:
        """Generate professional roles based on position."""
        position_name = requirements.get('position_name', '')
        seniority = requirements.get('seniority_level', 'Mid')

        roles = []

        # Base role
        roles.append(position_name)

        # Add related roles based on position type
        if 'AI' in position_name or 'Machine Learning' in position_name:
            roles.extend(['Machine Learning Engineer', 'Data Scientist', 'AI Researcher'])
        elif 'Engineer' in position_name:
            roles.extend(['Software Engineer', 'Full Stack Developer', 'Backend Developer'])
        elif 'Developer' in position_name:
            roles.extend(['Software Developer', 'Application Developer', 'Systems Developer'])

        # Add seniority-based roles
        if seniority in ['Senior', 'Lead', 'Principal']:
            roles.extend(['Technical Lead', 'Team Lead', 'Senior Consultant'])

        return list(set(roles))  # Remove duplicates
