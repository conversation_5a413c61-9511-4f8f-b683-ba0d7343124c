#!/usr/bin/env python3
"""
Advanced Lumus Response Processor with Chunked Markdown Generation.

This module processes the full raw Lumus API response and converts it to 
comprehensive markdown format with intelligent chunking for more accurate 
embeddings. This is a future enhancement that preserves ALL information 
from the Lumus response.

Key Features:
- Processes full raw Lumus JSON response (not just the processed candidate_info)
- Converts to comprehensive markdown with all sections
- Intelligent chunking for optimal embedding generation
- Preserves metadata and processing information
- Advanced PII filtering with context preservation
- Chunk overlap for semantic continuity
"""

import json
import logging
import re
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime

logger = logging.getLogger(__name__)


class LumusMarkdownProcessor:
    """
    Advanced processor for converting full Lumus responses to chunked markdown.
    
    This class handles the complete Lumus API response structure and converts
    it to optimally chunked markdown for enhanced embedding accuracy.
    """
    
    def __init__(self, 
                 chunk_size: int = 1500, 
                 chunk_overlap: int = 200,
                 max_chunks: int = 10):
        """
        Initialize the Lumus markdown processor.
        
        Args:
            chunk_size (int): Target size for each markdown chunk in characters
            chunk_overlap (int): Overlap between chunks for semantic continuity
            max_chunks (int): Maximum number of chunks to generate
        """
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.max_chunks = max_chunks
        
        # PII patterns for advanced filtering
        self.pii_patterns = {
            'email': re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'),
            'phone': re.compile(r'(\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})'),
            'ssn': re.compile(r'\b\d{3}-?\d{2}-?\d{4}\b'),
            'credit_card': re.compile(r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b'),
            'address': re.compile(r'\b\d+\s+[A-Za-z0-9\s,.-]+(?:Street|St|Avenue|Ave|Road|Rd|Boulevard|Blvd|Lane|Ln|Drive|Dr|Court|Ct|Place|Pl)\b', re.IGNORECASE)
        }
    
    def process_full_lumus_response(self, lumus_response: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process the complete Lumus API response into chunked markdown.
        
        Args:
            lumus_response (Dict[str, Any]): Full raw Lumus API response
            
        Returns:
            Dict[str, Any]: Processed result with chunks and metadata
        """
        try:
            logger.info(f"Processing full Lumus response for task: {lumus_response.get('task_id', 'unknown')}")
            
            # Extract metadata
            metadata = self._extract_metadata(lumus_response)
            
            # Extract candidate data
            candidate_data = self._extract_candidate_data(lumus_response)
            
            # Generate comprehensive markdown
            full_markdown = self._generate_comprehensive_markdown(candidate_data, metadata)
            
            # Create intelligent chunks
            chunks = self._create_intelligent_chunks(full_markdown)
            
            # Validate and optimize chunks
            optimized_chunks = self._optimize_chunks(chunks)
            
            result = {
                'success': True,
                'metadata': metadata,
                'full_markdown': full_markdown,
                'chunks': optimized_chunks,
                'chunk_count': len(optimized_chunks),
                'total_characters': len(full_markdown),
                'processing_timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"Successfully processed Lumus response into {len(optimized_chunks)} chunks")
            return result
            
        except Exception as e:
            logger.error(f"Error processing Lumus response: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'metadata': {},
                'chunks': [],
                'processing_timestamp': datetime.now().isoformat()
            }
    
    def _extract_metadata(self, lumus_response: Dict[str, Any]) -> Dict[str, Any]:
        """Extract metadata from the Lumus response."""
        return {
            'task_id': lumus_response.get('task_id'),
            'file_name': lumus_response.get('file_name'),
            'processing_time': lumus_response.get('processing_time'),
            'extraction_cost': lumus_response.get('result', {}).get('extraction_cost', {}),
            'status': lumus_response.get('status'),
            'created_at': lumus_response.get('created_at'),
            'completed_at': lumus_response.get('completed_at')
        }
    
    def _extract_candidate_data(self, lumus_response: Dict[str, Any]) -> Dict[str, Any]:
        """Extract candidate data from the Lumus response."""
        if 'result' in lumus_response and 'response' in lumus_response['result']:
            return lumus_response['result']['response']
        else:
            # Handle processed format
            return lumus_response
    
    def _generate_comprehensive_markdown(self, candidate_data: Dict[str, Any], metadata: Dict[str, Any]) -> str:
        """Generate comprehensive markdown from candidate data and metadata."""
        sections = []
        
        # Document metadata section
        if metadata.get('file_name'):
            sections.append(f"# CV Analysis: {metadata['file_name']}")
            sections.append(f"**Processing Date:** {datetime.now().strftime('%Y-%m-%d')}")
            if metadata.get('processing_time'):
                sections.append(f"**Processing Time:** {metadata['processing_time']:.2f} seconds")
            sections.append("")
        
        # Professional Summary
        if candidate_data.get('summary'):
            summary = self._filter_pii(candidate_data['summary'])
            sections.append("## Professional Summary")
            sections.append(summary)
            sections.append("")
        
        # Personal Information (filtered)
        personal_info = candidate_data.get('personal_info', {})
        if personal_info:
            sections.append("## Professional Profile")
            if personal_info.get('full_name'):
                # Replace actual name with placeholder for PII protection
                sections.append("**Professional Name:** [CANDIDATE_NAME]")
            
            # Location (city/country level only)
            location_parts = []
            if personal_info.get('city'):
                location_parts.append(personal_info['city'])
            if personal_info.get('country'):
                location_parts.append(personal_info['country'])
            if location_parts:
                sections.append(f"**Location:** {', '.join(location_parts)}")
            
            # Professional links (filtered)
            if personal_info.get('linkedin_profile'):
                sections.append("**LinkedIn:** [LINKEDIN_PROFILE]")
            if personal_info.get('website'):
                sections.append("**Website:** [PROFESSIONAL_WEBSITE]")
            sections.append("")
        
        # Education
        education = candidate_data.get('education', [])
        if education and any(edu.get('institution_name') or edu.get('degree') for edu in education):
            sections.append("## Education")
            for edu in education:
                if not edu or not any(edu.values()):
                    continue
                
                edu_parts = []
                if edu.get('degree') and edu.get('field_of_study'):
                    edu_parts.append(f"**{edu['degree']} in {edu['field_of_study']}**")
                elif edu.get('degree'):
                    edu_parts.append(f"**{edu['degree']}**")
                elif edu.get('field_of_study'):
                    edu_parts.append(f"**{edu['field_of_study']}**")
                
                if edu.get('institution_name'):
                    edu_parts.append(f"*{edu['institution_name']}*")
                
                if edu.get('start_date') or edu.get('end_date'):
                    date_range = f"({edu.get('start_date', '')} - {edu.get('end_date', '')})".replace(" - ", " - ").strip("()")
                    if date_range != "()":
                        edu_parts.append(f"({date_range})")
                
                if edu.get('location'):
                    edu_parts.append(f"- {edu['location']}")
                
                if edu_parts:
                    sections.append("- " + " ".join(edu_parts))
                
                if edu.get('description'):
                    clean_desc = self._filter_pii(edu['description'])
                    sections.append(f"  - {clean_desc}")
                
                sections.append("")
        
        # Work Experience
        work_exp = candidate_data.get('work_experience', [])
        if work_exp and any(job.get('job_title') or job.get('company_name') for job in work_exp):
            sections.append("## Work Experience")
            for job in work_exp:
                if not job or not (job.get('job_title') or job.get('company_name')):
                    continue
                
                # Job header
                job_title = job.get('job_title', 'Position')
                company = job.get('company_name', 'Company')
                start_date = job.get('start_date', '')
                end_date = job.get('end_date', '')
                location = job.get('location', '')
                
                header = f"### **{job_title}** at *{company}*"
                if start_date or end_date:
                    date_range = f"({start_date} - {end_date})".replace(" - ", " - ").strip("()")
                    if date_range != "()":
                        header += f" {date_range}"
                if location:
                    header += f" - {location}"
                
                sections.append(header)
                sections.append("")
                
                # Responsibilities
                responsibilities = job.get('responsibilities', [])
                if responsibilities:
                    sections.append("**Responsibilities:**")
                    for resp in responsibilities:
                        if resp and resp.strip():
                            clean_resp = self._filter_pii(resp)
                            sections.append(f"- {clean_resp}")
                    sections.append("")
                
                # Skills from work experience
                skills = job.get('skills', [])
                if skills:
                    skill_names = []
                    for skill in skills:
                        if isinstance(skill, dict) and skill.get('name'):
                            skill_names.append(skill['name'])
                        elif isinstance(skill, str):
                            skill_names.append(skill)
                    
                    if skill_names:
                        sections.append(f"**Technologies Used:** {', '.join(skill_names)}")
                        sections.append("")
        
        # Technical Skills
        skills = candidate_data.get('skills', [])
        if skills:
            sections.append("## Technical Skills")
            skill_names = []
            for skill in skills:
                if isinstance(skill, dict) and skill.get('name'):
                    skill_names.append(skill['name'])
                elif isinstance(skill, str):
                    skill_names.append(skill)
            
            if skill_names:
                sections.append(", ".join(skill_names))
                sections.append("")
        
        # Soft Skills
        soft_skills = candidate_data.get('soft_skills', [])
        if soft_skills:
            sections.append("## Soft Skills")
            soft_skill_names = []
            for skill in soft_skills:
                if isinstance(skill, dict) and skill.get('name'):
                    soft_skill_names.append(skill['name'])
                elif isinstance(skill, str):
                    soft_skill_names.append(skill)
            
            if soft_skill_names:
                sections.append(", ".join(soft_skill_names))
                sections.append("")
        
        # Certifications
        certifications = candidate_data.get('certifications', [])
        if certifications and any(cert.get('name') for cert in certifications):
            sections.append("## Certifications")
            for cert in certifications:
                if not cert or not cert.get('name'):
                    continue
                
                cert_line = f"- **{cert['name']}**"
                if cert.get('issuing_organization'):
                    cert_line += f" - *{cert['issuing_organization']}*"
                if cert.get('issue_date'):
                    cert_line += f" ({cert['issue_date']})"
                
                sections.append(cert_line)
            sections.append("")
        
        # Languages
        languages = candidate_data.get('languages', [])
        if languages:
            sections.append("## Languages")
            for lang in languages:
                if isinstance(lang, dict):
                    lang_name = lang.get('language', '')
                    proficiency = lang.get('proficiency_level', '')
                    if lang_name:
                        lang_line = f"- **{lang_name}**"
                        if proficiency:
                            lang_line += f" ({proficiency})"
                        sections.append(lang_line)
                elif isinstance(lang, str):
                    sections.append(f"- **{lang}**")
            sections.append("")
        
        # Projects
        projects = candidate_data.get('projects', [])
        if projects and any(proj.get('name') for proj in projects):
            sections.append("## Projects")
            for proj in projects:
                if not proj or not proj.get('name'):
                    continue
                
                sections.append(f"### {proj['name']}")
                
                if proj.get('description'):
                    clean_desc = self._filter_pii(proj['description'])
                    sections.append(clean_desc)
                
                if proj.get('technologies_used'):
                    tech_list = proj['technologies_used']
                    if isinstance(tech_list, list):
                        sections.append(f"**Technologies:** {', '.join(tech_list)}")
                    elif isinstance(tech_list, str):
                        sections.append(f"**Technologies:** {tech_list}")
                
                if proj.get('role'):
                    sections.append(f"**Role:** {proj['role']}")
                
                sections.append("")
        
        # Professional Roles
        roles = candidate_data.get('roles', [])
        if roles:
            sections.append("## Professional Roles")
            sections.append(", ".join(roles))
            sections.append("")
        
        return "\n".join(sections)
    
    def _filter_pii(self, text: str) -> str:
        """Filter PII from text while preserving professional context."""
        if not text:
            return ""
        
        filtered_text = text
        
        # Replace PII patterns with placeholders
        for pii_type, pattern in self.pii_patterns.items():
            if pii_type == 'email':
                filtered_text = pattern.sub('[EMAIL_ADDRESS]', filtered_text)
            elif pii_type == 'phone':
                filtered_text = pattern.sub('[PHONE_NUMBER]', filtered_text)
            elif pii_type == 'ssn':
                filtered_text = pattern.sub('[SSN]', filtered_text)
            elif pii_type == 'credit_card':
                filtered_text = pattern.sub('[CREDIT_CARD]', filtered_text)
            elif pii_type == 'address':
                # Be more conservative with addresses - only replace obvious street addresses
                filtered_text = pattern.sub('[STREET_ADDRESS]', filtered_text)
        
        return filtered_text
    
    def _create_intelligent_chunks(self, markdown_text: str) -> List[Dict[str, Any]]:
        """Create intelligent chunks from markdown text."""
        chunks = []
        lines = markdown_text.split('\n')
        current_chunk = []
        current_size = 0
        chunk_index = 0
        
        section_header = ""
        
        for line in lines:
            line_size = len(line) + 1  # +1 for newline
            
            # Track section headers for context
            if line.startswith('#'):
                section_header = line.strip('#').strip()
            
            # Check if adding this line would exceed chunk size
            if current_size + line_size > self.chunk_size and current_chunk:
                # Create chunk
                chunk_text = '\n'.join(current_chunk)
                chunks.append({
                    'index': chunk_index,
                    'text': chunk_text,
                    'size': current_size,
                    'section': section_header,
                    'lines': len(current_chunk)
                })
                
                chunk_index += 1
                
                # Start new chunk with overlap
                overlap_lines = self._get_overlap_lines(current_chunk)
                current_chunk = overlap_lines
                current_size = sum(len(line) + 1 for line in overlap_lines)
            
            current_chunk.append(line)
            current_size += line_size
            
            # Stop if we've reached max chunks
            if len(chunks) >= self.max_chunks:
                break
        
        # Add final chunk if there's content
        if current_chunk and len(chunks) < self.max_chunks:
            chunk_text = '\n'.join(current_chunk)
            chunks.append({
                'index': chunk_index,
                'text': chunk_text,
                'size': current_size,
                'section': section_header,
                'lines': len(current_chunk)
            })
        
        return chunks
    
    def _get_overlap_lines(self, lines: List[str]) -> List[str]:
        """Get overlap lines for semantic continuity."""
        if not lines:
            return []
        
        overlap_chars = 0
        overlap_lines = []
        
        # Start from the end and work backwards
        for line in reversed(lines):
            line_size = len(line) + 1
            if overlap_chars + line_size <= self.chunk_overlap:
                overlap_lines.insert(0, line)
                overlap_chars += line_size
            else:
                break
        
        return overlap_lines
    
    def _optimize_chunks(self, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Optimize chunks for better embedding quality."""
        optimized = []
        
        for chunk in chunks:
            # Add metadata to each chunk
            chunk['word_count'] = len(chunk['text'].split())
            chunk['has_section_header'] = any(line.startswith('#') for line in chunk['text'].split('\n'))
            chunk['content_type'] = self._identify_content_type(chunk['text'])
            
            # Only include chunks with meaningful content
            if chunk['word_count'] >= 10:  # Minimum word threshold
                optimized.append(chunk)
        
        return optimized
    
    def _identify_content_type(self, text: str) -> str:
        """Identify the primary content type of a chunk."""
        if '## Work Experience' in text or '### **' in text:
            return 'work_experience'
        elif '## Education' in text:
            return 'education'
        elif '## Technical Skills' in text or '## Skills' in text:
            return 'skills'
        elif '## Projects' in text:
            return 'projects'
        elif '## Professional Summary' in text:
            return 'summary'
        elif '## Certifications' in text:
            return 'certifications'
        elif '## Languages' in text:
            return 'languages'
        else:
            return 'general'
