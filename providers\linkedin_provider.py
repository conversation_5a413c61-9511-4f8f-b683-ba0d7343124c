"""
LinkedIn provider implementation for professional data search.
Returns mock data for development and testing purposes.
"""

import logging
from typing import Dict, Any, List
from providers.base_provider import BaseProvider, SearchRequest, ProviderResponse

# Setup logging
logger = logging.getLogger(__name__)


class LinkedInProvider(BaseProvider):
    """
    LinkedIn provider implementation using the Adapter Pattern.
    Currently returns mock data - can be extended to use actual LinkedIn API.
    """
    
    def __init__(self):
        super().__init__("linkedin")
        logger.info("LinkedIn provider initialized")
    
    async def search_professionals(self, request: SearchRequest) -> ProviderResponse:
        """
        Search for professionals on LinkedIn (mock implementation).
        
        Args:
            request: Search request with query, filters, etc.
            
        Returns:
            ProviderResponse: Standardized response with mock LinkedIn data
        """
        logger.info(f"Searching LinkedIn for: {request.query}")
        
        # Mock LinkedIn data - in production, this would call LinkedIn API
        mock_linkedin_data = self._get_mock_linkedin_data(request)
        
        # Adapt each professional's data to our standard format
        adapted_professionals = []
        for linkedin_prof in mock_linkedin_data:
            adapted_prof = self.adapt_to_professional_model(linkedin_prof)
            adapted_professionals.append(adapted_prof)
        
        return ProviderResponse(
            professionals=adapted_professionals,
            total_found=len(adapted_professionals),
            provider_name=self.provider_name,
            search_metadata={
                "query": request.query,
                "location": request.location,
                "skills_filter": request.skills,
                "experience_level": request.experience_level
            }
        )
    
    def adapt_to_professional_model(self, linkedin_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Adapt LinkedIn-specific data format to our Professional model structure.
        This is the core adapter method that transforms LinkedIn data.
        
        Args:
            linkedin_data: Raw LinkedIn profile data
            
        Returns:
            Dict: Data formatted to match our Professional model
        """
        # Transform LinkedIn data to our Professional model format
        adapted_data = {
            "source": "linkedin",
            "roles": self._extract_roles(linkedin_data),
            "skills": self._adapt_skills(linkedin_data.get("skills", [])),
            "summary": linkedin_data.get("summary", ""),
            "projects": linkedin_data.get("projects"),
            "education": self._adapt_education(linkedin_data.get("education", [])),
            "languages": linkedin_data.get("languages"),
            "references": linkedin_data.get("references"),
            "soft_skills": linkedin_data.get("soft_skills"),
            "personal_info": self._adapt_personal_info(linkedin_data.get("personal_info", {})),
            "certifications": self._adapt_certifications(linkedin_data.get("certifications", [])),
            "work_experience": self._adapt_work_experience(linkedin_data.get("work_experience", []))
        }
        
        # Generate tags for search filtering
        adapted_data["tags"] = self.generate_tags(adapted_data)
        
        return adapted_data
    
    def _get_mock_linkedin_data(self, request: SearchRequest) -> List[Dict[str, Any]]:
        """Generate mock LinkedIn data based on search request"""
        
        # Mock data that simulates LinkedIn API responses
        mock_profiles = [
            {
                "linkedin_id": "alex-guigni-cedano",
                "summary": "Software QA Analyst with more than 10 years executing the necessary processes to assess and ensure the quality of different types of systems for industries such as banking, accounting, logistics and international shipping, personnel management, medical, sports betting, etc. I have experience in design, creation and updating test documentation, requirement analysis, tasks estimation, execution of various types of functional, non-functional, manual, and automated testing (Selenium and Java, even basic Appium knowledge). In the last years I have worked as QA Lead, implementing, and improving software quality processes, training, and leading QA teams.",
                "personal_info": {
                    "full_name": "Alexander Guigni Cedano",
                    "email": "<EMAIL>",
                    "phone_number": "(506)********",
                    "city": "Pueblo Nuevo, Alajuela, Alajuela",
                    "country": "Costa Rica",
                    "linkedin_profile": "https://linkedin.com/in/alex-guigni-cedano",
                    "website": "",
                    "address": ""
                },
                "skills": [
                    {"name": "Appium", "endorsements": 15, "years_experience": 2.0},
                    {"name": "Java", "endorsements": 25, "years_experience": 4.0},
                    {"name": "C#", "endorsements": 12, "years_experience": 2.0},
                    {"name": "Selenium", "endorsements": 30, "years_experience": 5.0},
                    {"name": "Quality Assurance", "endorsements": 45, "years_experience": 10.0},
                    {"name": "Test Automation", "endorsements": 35, "years_experience": 6.0}
                ],
                "work_experience": [
                    {
                        "company_name": "Advision Development",
                        "job_title": "QA Lead",
                        "location": "San Jose, Costa Rica",
                        "start_date": "March 2021",
                        "end_date": "Present",
                        "description": "Lead the QA team (Manual and test automation) so that all activities required for software quality assurance are executed effectively and efficiently.",
                        "skills_used": ["Appium", "Selenium", "Java", "Team Leadership"]
                    },
                    {
                        "company_name": "Establishment Labs",
                        "job_title": "IT QA Analyst Lead",
                        "location": "San Jose, Costa Rica",
                        "start_date": "February 2019",
                        "end_date": "March 2021",
                        "description": "QA lead in charge of all the QA process related to IT department. I work across teams to ensure IT system deployments are implemented with minimal impact to production operation.",
                        "skills_used": ["Selenium", "Java", "System Testing"]
                    }
                ],
                "education": [
                    {
                        "institution_name": "UNED",
                        "degree": "In progress",
                        "field_of_study": "Computer Science",
                        "start_date": "2020",
                        "end_date": "",
                        "location": "",
                        "description": ""
                    }
                ],
                "certifications": [
                    {
                        "name": "Appium -Mobile Testing (Android/IOS) from Scratch+Frameworks",
                        "issuing_organization": "Udemy",
                        "issue_date": "2022",
                        "credential_id": "",
                        "credential_url": "",
                        "expiration_date": ""
                    }
                ],
                "languages": ["Spanish", "English"],
                "projects": None,
                "references": None,
                "soft_skills": ["Leadership", "Team Management", "Problem Solving"]
            },
            {
                "linkedin_id": "maria-rodriguez-dev",
                "summary": "Full Stack Developer with 8+ years of experience in web development, specializing in React, Node.js, and cloud technologies. Passionate about creating scalable applications and mentoring junior developers.",
                "personal_info": {
                    "full_name": "Maria Rodriguez",
                    "email": "<EMAIL>",
                    "phone_number": "******-0123",
                    "city": "Austin, Texas",
                    "country": "United States",
                    "linkedin_profile": "https://linkedin.com/in/maria-rodriguez-dev",
                    "website": "https://mariadev.com",
                    "address": ""
                },
                "skills": [
                    {"name": "React", "endorsements": 40, "years_experience": 6.0},
                    {"name": "Node.js", "endorsements": 35, "years_experience": 5.0},
                    {"name": "JavaScript", "endorsements": 50, "years_experience": 8.0},
                    {"name": "TypeScript", "endorsements": 30, "years_experience": 4.0},
                    {"name": "AWS", "endorsements": 25, "years_experience": 3.0},
                    {"name": "MongoDB", "endorsements": 20, "years_experience": 4.0}
                ],
                "work_experience": [
                    {
                        "company_name": "TechCorp Solutions",
                        "job_title": "Senior Full Stack Developer",
                        "location": "Austin, TX",
                        "start_date": "January 2020",
                        "end_date": "Present",
                        "description": "Lead development of enterprise web applications using React and Node.js. Mentor junior developers and architect scalable solutions.",
                        "skills_used": ["React", "Node.js", "AWS", "TypeScript"]
                    }
                ],
                "education": [
                    {
                        "institution_name": "University of Texas at Austin",
                        "degree": "Bachelor of Science",
                        "field_of_study": "Computer Science",
                        "start_date": "2012",
                        "end_date": "2016",
                        "location": "Austin, TX",
                        "description": ""
                    }
                ],
                "certifications": [
                    {
                        "name": "AWS Certified Solutions Architect",
                        "issuing_organization": "Amazon Web Services",
                        "issue_date": "2021",
                        "credential_id": "AWS-CSA-2021-001",
                        "credential_url": "",
                        "expiration_date": "2024"
                    }
                ],
                "languages": ["English", "Spanish"],
                "projects": None,
                "references": None,
                "soft_skills": ["Leadership", "Mentoring", "Communication"]
            }
        ]
        
        # Filter based on request (simple mock filtering)
        filtered_profiles = []
        for profile in mock_profiles:
            if self._matches_search_criteria(profile, request):
                filtered_profiles.append(profile)
        
        return filtered_profiles[:request.limit or 10]
    
    def _matches_search_criteria(self, profile: Dict[str, Any], request: SearchRequest) -> bool:
        """Simple mock filtering logic"""
        query_lower = request.query.lower()
        
        # Check if query matches in summary, skills, or job titles
        if query_lower in profile.get("summary", "").lower():
            return True
        
        # Check skills
        for skill in profile.get("skills", []):
            if query_lower in skill.get("name", "").lower():
                return True
        
        # Check work experience
        for exp in profile.get("work_experience", []):
            if query_lower in exp.get("job_title", "").lower():
                return True
        
        return False
    
    def _extract_roles(self, linkedin_data: Dict[str, Any]) -> List[str]:
        """Extract roles from LinkedIn work experience"""
        roles = []
        for exp in linkedin_data.get("work_experience", []):
            if "job_title" in exp:
                roles.append(exp["job_title"])
        return roles
    
    def _adapt_skills(self, linkedin_skills: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Adapt LinkedIn skills to our format"""
        adapted_skills = []
        for skill in linkedin_skills:
            adapted_skill = {
                "name": skill.get("name", ""),
                "proficiency_level": "NOT DEFINED",  # LinkedIn doesn't provide this
                "years_of_experience": skill.get("years_experience", 0.0)
            }
            adapted_skills.append(adapted_skill)
        return adapted_skills
    
    def _adapt_education(self, linkedin_education: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Adapt LinkedIn education to our format"""
        adapted_education = []
        for edu in linkedin_education:
            adapted_edu = {
                "institution_name": edu.get("institution_name", ""),
                "degree": edu.get("degree", ""),
                "field_of_study": edu.get("field_of_study", ""),
                "start_date": edu.get("start_date", ""),
                "end_date": edu.get("end_date", ""),
                "location": edu.get("location", ""),
                "description": edu.get("description", "")
            }
            adapted_education.append(adapted_edu)
        return adapted_education
    
    def _adapt_personal_info(self, linkedin_personal: Dict[str, Any]) -> Dict[str, Any]:
        """Adapt LinkedIn personal info to our format"""
        return {
            "full_name": linkedin_personal.get("full_name", ""),
            "email": linkedin_personal.get("email", ""),
            "phone_number": linkedin_personal.get("phone_number", ""),
            "city": linkedin_personal.get("city", ""),
            "country": linkedin_personal.get("country", ""),
            "linkedin_profile": linkedin_personal.get("linkedin_profile", ""),
            "website": linkedin_personal.get("website", ""),
            "address": linkedin_personal.get("address", "")
        }
    
    def _adapt_certifications(self, linkedin_certs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Adapt LinkedIn certifications to our format"""
        adapted_certs = []
        for cert in linkedin_certs:
            adapted_cert = {
                "name": cert.get("name", ""),
                "issuing_organization": cert.get("issuing_organization", ""),
                "issue_date": cert.get("issue_date", ""),
                "credential_id": cert.get("credential_id", ""),
                "credential_url": cert.get("credential_url", ""),
                "expiration_date": cert.get("expiration_date", "")
            }
            adapted_certs.append(adapted_cert)
        return adapted_certs
    
    def _adapt_work_experience(self, linkedin_work: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Adapt LinkedIn work experience to our format"""
        adapted_work = []
        for work in linkedin_work:
            # Convert skills_used to our skills format
            skills_used = []
            for skill_name in work.get("skills_used", []):
                skills_used.append({
                    "name": skill_name,
                    "proficiency_level": "NOT DEFINED",
                    "years_of_experience": 2.0  # Default value
                })
            
            adapted_exp = {
                "company_name": work.get("company_name", ""),
                "job_title": work.get("job_title", ""),
                "location": work.get("location", ""),
                "start_date": work.get("start_date", ""),
                "end_date": work.get("end_date", ""),
                "skills": skills_used,
                "responsibilities": [work.get("description", "")] if work.get("description") else []
            }
            adapted_work.append(adapted_exp)
        return adapted_work
